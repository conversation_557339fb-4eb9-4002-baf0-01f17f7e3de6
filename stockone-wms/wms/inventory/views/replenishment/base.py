# python imports
import numpy as np
import pandas as pd
import math
import datetime
from collections import OrderedDict

# django imports
from django.db.models import Q, Sum, F, Max
from django.utils import timezone


from rest_api.views.common_operations import perform_bulk_deletions
from rest_api.views import get_multiple_misc_values, create_order_new
from rest_api.views.stock_locator import get_current_stock
from rest_api.views.inbound import get_sku_wise_intransit_quantity_df

from miebach_admin.models import ( 
    ReplenushmentMaster, ClosingStock, OrderDetail,
    SKUAttributes, SellerMaster, SKUMaster, CustomerMaster,
    SupplierMaster, User, SkuClassification, StockDetail,
    TaxMaster, LocationMaster, ForecastMaster, BOMMaster,
    SKUSupplier
)


class FMSClassification():
    def __init__(self, user_id):
        self.user_id = user_id

    def prerequisite_for_abc_calculation(self, orders, closing_stock, days=7):
        results = closing_stock.merge(orders, on=['sku_id', 'creation_date__date'], how='left')
        results = results.fillna(0)
        results['transaction_date'] = pd.to_datetime(results['creation_date__date'])
        df = results.groupby(by=['sku_id']).apply(lambda x: x.set_index('transaction_date').resample('1D').first())
        df = df.fillna(0)
        df = df[df['creation_date__date'] != 0]
        avg_cal_days = f'avg_{days}_days'
        df1 = df.groupby(level=0)['order_quantity'].apply(
            lambda x: x.shift().rolling(min_periods=3,window=days).mean()
            ).reset_index(name=avg_cal_days)
        df2 = pd.merge(results, df1, on=['transaction_date', 'sku_id'], how='left')
        df2 = df2.dropna()
        df3 = df2.loc[df2.groupby('sku_id').transaction_date.idxmax()]
        return df3

    def abc(self, df, metric_column, abc_class_name='class'):
        """Assign an ABC class and rank to a metric based on cumulative percentage contribution. 
        
        Args:
            df: Pandas dataframe containing data. 
            metric_column (string): Name of column containing metric to calculate. 
            abc_class_name (string, optional): Name to assign to class column. 
        
        Return:
            Pandas dataframe containing original data, plus the metric class and rank. 
        """
        
        def _abc_segment(percentage):
            """Assign an ABC segment based on cumulative percentage contribution.
            Args:
                percentage (float): Cumulative percentage of ranked metric.
            Returns:
                segments: Pandas DataFrame
            """

            if 0 < percentage <= 40:
                return 'Fast'
            elif 40 < percentage <= 80:
                return 'Medium'
            else:
                return 'Slow'    

        df = df.fillna(0)
        data = df.sort_values(by=metric_column, ascending=False)
        data[metric_column+'_sum'] = data[metric_column].sum()
        data[metric_column+'_cumsum'] = data[metric_column].cumsum()
        data[metric_column+'_running_pc'] = (data[metric_column+'_cumsum'] / data[metric_column+'_sum']) * 100
        data[abc_class_name] = data[metric_column+'_running_pc'].apply(_abc_segment)
        data = data.fillna(0)
        data[abc_class_name+'_rank'] = data[metric_column+'_running_pc'].rank().astype(int)
        return data

    def get_order_data(self, sku_ids, days=30, parse_date=None):
        default_values_list = ['sku_id', 'creation_date__date']
        if parse_date:
            default_values_list[1] = 'creation_date'

        sale_orders_qs = OrderDetail.objects.filter(
                sku_id__in=sku_ids,
                creation_date__gt=timezone.now() - datetime.timedelta(days=days)
            ).values(
                *default_values_list
            ).annotate(
                order_quantity=Sum('original_quantity')
            )
        if parse_date:
            sale_orders_qs = list(sale_orders_qs)
            for each_row in sale_orders_qs:
                each_row['creation_date__date'] = each_row['creation_date'].date()
                each_row.pop('creation_date', None)

        return pd.DataFrame(sale_orders_qs)

    def get_closing_stock(self, sku_ids, days=30, zones=None):
        basic_filter = {
            'stock__sku_id__in': sku_ids,
            'closing_date__gt': datetime.datetime.today()-datetime.timedelta(days=days)
        }
        if zones:
            basic_filter['stock__location__zone__zone__in'] = zones
        stock_query = ClosingStock.objects.filter(
                **basic_filter
            ).values(
                creation_date__date=F('closing_date')
            ).annotate(
                sku_id=F('stock__sku_id'),
                closing_stock=Sum('quantity')
            ).filter(
                closing_stock__gt=0
            )
        return pd.DataFrame(stock_query)


class Replenishment():
    def __init__(self, user_id) -> None:
        self.user_id = user_id
        self.user = User.objects.filter(id=user_id)

    def get_replenishment_data(self):
        replenish_data = pd.DataFrame(ReplenushmentMaster.objects.filter(
            user_id=self.user_id,
            size=3,
        ).values(
            'classification',
            'size',
            'min_days',
            'max_days'
        ))
        replenish_data = replenish_data.append(
            {"classification": "Other", "max_days": 30, "min_days": 20}, 
            ignore_index = True
        )
        return replenish_data

    def replenishment(self, abcdf, current_stock, replenishment_master, on_key='avg_7_days', default_skus=None):
        if default_skus:
            rows = []
            for sku in default_skus:
                row = {'sku_id': sku, 
                    'classification': 'Other',
                    on_key: 1
                }
                rows.append(row)
            rowsdf = pd.DataFrame(rows)
            abcdf = pd.concat([abcdf, rowsdf], ignore_index = True)
            abcdf.reset_index()

        if not current_stock.empty:
            res = abcdf.merge(
                current_stock, on=['sku_id'],
                how='left'
            ).merge(
                replenishment_master, on='classification',
                how='left'
            )
        else:
            res = abcdf.merge(
                replenishment_master, on='classification',
                how='left'
            )
            res['available_qty'] = 0
            
        res = res.fillna(0)
        res['max_qty'] = res['max_days'] * res[on_key]
        res['min_qty'] = res['min_days'] * res[on_key]
        res['assumed_available_qty'] = np.where(res['available_qty'] == 1, 0, res['available_qty'])
        res['rep_qty'] = np.where(res['assumed_available_qty'] <= res['min_qty'], res['max_qty'] - res['assumed_available_qty'] , 0)
        res['remarks'] = np.where(
            (res['assumed_available_qty'] <= res['min_qty']) & (res['assumed_available_qty']),
            'Available Quantity is more than Min Stock', 
            'Sale Area Has Sufficent Stock'
        )
        res = res.fillna(0)
        if "pending_grn_qty" in res.columns:
            res['rep_qty'] = res['rep_qty'] - res['pending_grn_qty']
            res['rep_qty'] = np.where(res['rep_qty'] < 0, 0, res['rep_qty'])
        self.replenished_data = res
        return res

    def set_sku_classification_data(self, sku_data, on_key):
        sku_classification_maps = [
            ('sku_id', 'sku_id', None),
            ('avg_sales_day', on_key, 0),
            ('cumulative_contribution', '%s_cumsum' % on_key, 0),
            ('classification', 'classification', None),
            ('replenushment_qty', 'rep_qty', 0),
            ('sku_avail_qty', 'available_qty', 0),
            ('min_stock_qty', 'min_qty', 0),
            ('max_stock_qty', 'max_qty', 0),
            ('remarks', 'remarks', ''),
            ('pending_grn_qty', 'pending_grn_qty', 0),
            ('pending_putaway_qty', 'pending_putaway_qty', 0),
        ]
        sku_classification_dict = {}
        for model_key, df_key, default_value in sku_classification_maps:
            sku_classification_dict[model_key] = sku_data.get(df_key, default_value)

        return sku_classification_dict
    
    def save_sku_classification_data(self, df, on_key='avg_7_days'):
        # fixing float values
        default_float_keys = ('rep_qty', 'min_qty', 'max_qty')
        for key in default_float_keys:
            df[key] = np.ceil(df[key]).astype(float)

        # processing
        sku_classification_objs = []
        for index, row in df.iterrows():
            try:
                row = row.to_dict()
                sku_pen_putaway_qty = row.get('pending_putaway_qty', 0) + row.get('return_putaway_qty', 0)
            except:
                sku_pen_putaway_qty = 0
            row['pending_putaway_qty'] = sku_pen_putaway_qty
            sku_classification_dict = self.set_sku_classification_data(row, on_key)
            sku_classification_objs.append(SkuClassification(**sku_classification_dict))

        sku_cf_qs = SkuClassification.objects.filter(sku__user=self.user_id)
        perform_bulk_deletions(sku_cf_qs, 100)
        SkuClassification.objects.bulk_create(sku_classification_objs)


class ReplenishmentMixin():
    def __init__(self, user_id, automate=False) -> None:
        self.automate = automate
        self.user_id = user_id
        self.user = User.objects.get(id=user_id)

    def set_destination_locations(self):
        sa_zones = self.misc_values_dict.get("pick_zones_list") or []
        if sa_zones:
            sa_zones = sa_zones.split(',')
        
        self.dest_locations = list(LocationMaster.objects.filter(
            zone__user=self.user.id,
            zone__zone__in=sa_zones, status=1)
        )

    def prepare_replenishment(self):
        if self.main_warehouse:
            children_requirement = self.get_replenishment_requirement(child=True)
            children_requirement['main_user_id'] = self.user_id
            children_requirement['main_user_username'] = self.user.username
            merged_with_customers = self.attach_main_wh_customers_for_children(children_requirement)
            merged_with_supplier = self.attach_childsupplier_for_main(merged_with_customers)
            self.pure_requirement = self.merge_with_master_sku(merged_with_supplier)
            if "main_sku_id" in self.pure_requirement.columns:
                self.pure_requirement['ba_sku_id'] = self.pure_requirement['main_sku_id']
        else:
            self.pure_requirement = self.get_replenishment_requirement(child=False)
            if not self.pure_requirement.empty:
                self.pure_requirement['ba_sku_id'] = self.pure_requirement['sku_id']

    def set_replenishment(self, bulk_move_intentory=True):
        bulk_status , bulk_stock = self.get_bulk_stock(self.pure_requirement['ba_sku_id'].values.tolist())
        if not bulk_status:
            self.pure_requirement['remarks'] = bulk_stock
            return bulk_stock

        if self.main_warehouse:
            pure_req = self.pure_requirement.merge(bulk_stock, how="inner", on="ba_sku_id")
            pure_req = self.fill_empty_mrp_weight(pure_req)
            pure_req = pure_req.groupby(by=['child_sku_id', 'child_user_id', 'sku_code']).max().reset_index()
            payload = self.prepare_payload_for_order_creation(pure_req)
            order_response = create_order_new(self.user, payload, is_limit=True)
            pure_req['orders_created'] = 1
            self.final_requirement = pure_req

    def get_bulk_stock(self, sku_ids):
        bulk_zones = self.misc_values_dict.get('bulk_zones_list', '')
        if not bulk_zones:
            return False, "No Bulk Zone Defined"

        bulk_zones = bulk_zones.split(",")
        bulk_stock = pd.DataFrame(
            StockDetail.objects.filter(
                sku_id__in=sku_ids, 
                location__zone__zone__in=bulk_zones,
                quantity__gt=0
            ).values(
                'batch_detail__buy_price',
                'batch_detail__mrp',
                'batch_detail__weight'
            ).annotate(
                stock_id=F('id'),
                ba_sku_id=F('sku_id'),
                stock_quantity=F('quantity')
            )
        )
        if bulk_stock.empty:
            return False, "No Stock In Bulk Area"
        return True, bulk_stock

    def merge_with_master_sku(self, child_whs_full_df):
        main_user_sku_ids = dict(
            SKUMaster.objects.filter(
                user=self.user_id,
            ).values_list("sku_code", "id")
        )
        child_whs_full_df['main_sku_id'] = child_whs_full_df['sku_code']
        child_whs_full_df = child_whs_full_df.replace({"main_sku_id": main_user_sku_ids})
        main_user_sku_stocks_df = get_current_stock(main_user_sku_ids.values())
        main_wh_open_orders_df = self.get_open_orders(self.user_id, main_user_sku_ids.values())
        if not main_wh_open_orders_df.empty:
            main_user_sku_stocks_df = main_user_sku_stocks_df.merge(main_wh_open_orders_df, how="left", on="sku_id")
            main_user_sku_stocks_df['open_order_qty'] = main_user_sku_stocks_df['open_order_qty'].fillna(0)

        merged_df = child_whs_full_df.merge(main_user_sku_stocks_df, how="inner", left_on="main_sku_id", right_on="sku_id")
        if main_wh_open_orders_df.empty:
            merged_df['open_order_qty'] = 0
        merged_df['replenushment_qty'] = merged_df['replenushment_qty'] - merged_df['open_order_qty']
        merged_df['replenushment_qty'] = np.where(merged_df['replenushment_qty'] > 0, merged_df['replenushment_qty'] , 0)
        merged_df = merged_df[merged_df['replenushment_qty'] > 0]
        return merged_df

    def attach_childsupplier_for_main(self, mwcDf):
        supDf = pd.DataFrame(
            SupplierMaster.objects.filter(
                user__in=self.children_ids,
                supplier_type="Internal",
                supplier_id=self.user.username
            ).values(
                'id'
            ).annotate(
                child_user_id=F("user"),
                po_supplier_id=F("supplier_id"),
                supplier_id=F("id"),
                supplier_state=F("state"),
                supplier_type=F("supplier_type")
            )
        )
        mergedDf = mwcDf.merge(supDf, how="left")
        return mergedDf

    def attach_main_wh_customers_for_children(self, chR):
        cusDf = pd.DataFrame(
            CustomerMaster.objects.filter(
                user=self.user_id,
                customer_type="Stock Transfer",
                customer_code__in=self.wh_usernames
            ).values(
                'customer_code'
            ).annotate(
                cust_customer_code=F("customer_code"),
                cust_customer_id=F("customer_id"),
                cust_name=F("name"),
                cust_email_id=F("email_id"),
                cust_phone_number=F("phone_number")
            )
        )
        mergedDf = chR.merge(cusDf, how="left", left_on="child_user_username", right_on="cust_customer_code")
        return mergedDf
    
    def get_tax_type(self):
        return pd.DataFrame(
            TaxMaster.objects.filter(user_id__in=self.children_ids, inter_state=1).values(
                "product_type",
                "min_amt", "max_amt", 'cgst_tax', 'sgst_tax', 'igst_tax', 'utgst_tax'
            ).annotate(
                child_user_id=F("user_id")
            )
        )

    def set_tax_type(self, taxDf, rep_row):
        unit_price =  rep_row['ba_sku_buy_price'] * rep_row['replenushment_qty']
        newTaxDf = taxDf[(taxDf["min_amt"] < unit_price) & (taxDf["max_amt"] >= unit_price)]
        if newTaxDf.empty:
            return  {
                'cgst_tax': 0, 
                'sgst_tax': 0, 
                'igst_tax': 0, 
                'utgst_tax': 0
            }
        return newTaxDf.iloc[1].to_dict()

    def get_sku_classification(self, users):
        return SkuClassification.objects.filter(
                sku__user__in=users,
                creation_date__date=datetime.datetime.today(),
                replenushment_qty__gt=0)

    def get_replenishment_requirement(self, child):
        users = [self.user_id]
        if child:
            users = self.children_ids
        skuCdf = pd.DataFrame(
            self.get_sku_classification(users).values(
                'sku__id',
                'sku__user',
                'sku__sku_code',
                'sku__sku_category',
                'sku__mrp',
                'sku__cost_price',
                'sku__product_type',
                'sku__sku_desc',
                'avg_sales_day',
                'cumulative_contribution',
                'classification',
                'replenushment_qty',
                'sku_avail_qty',
                'min_stock_qty',
                'max_stock_qty',
                'remarks'
            ).annotate(
                sku_weight=Max('sku__skuattributes__attribute_value', filter=Q(sku__skuattributes__attribute_name='Weight'))
            )
        )
        if skuCdf.empty:
            return skuCdf

        skuCdf = skuCdf.rename(columns={
            'sku__sku_desc': 'sku_desc',
            'sku__id': 'sku_id',
            'sku__user': 'user_id',
            'sku__sku_code': 'sku_code',
            'sku__sku_category': 'sku_category',
            'sku__cost_price': 'cost_price',
            'sku__mrp': 'mrp'
        })

        if child:
            skuCdf = skuCdf.rename(columns={
                'sku_id': 'child_sku_id',
                'user_id': 'child_user_id'
            })
            skuCdf['child_user_username'] = skuCdf['child_user_id']
            skuCdf = skuCdf.replace({"child_user_username": self.children_id_map})
        return skuCdf

    def run_store_replenushment_workflow(self, user_id, average_days, parse_date=False):
        bulk_zones = self.misc_values_dict.get("bulk_zones_list")
        if bulk_zones:
            bulk_zones = bulk_zones.split(',')
        sa_zones = self.misc_values_dict.get("pick_zones_list")
        if sa_zones:
            sa_zones = sa_zones.split(',')
        closing_stock_zones = self.misc_values_dict.get("closing_stock_zones")
        if closing_stock_zones:
            closing_stock_zones = closing_stock_zones.split(',')
        fmsc = FMSClassification(user_id) # initialize FMSC Classification
        sku_ids = self.get_replenushment_sku_ids(user_id) # Custom For MB as SKU attribute filters are applied will be limited
        orders = fmsc.get_order_data(sku_ids, days=30, parse_date=parse_date) # Procuring Sale Data of Last 30 days
        closing_stock = fmsc.get_closing_stock(sku_ids, zones=closing_stock_zones) # Procuring Closing Stock Of Last 30 Days and Bulk Zones
        # Adding One Day , to make it opening stock of the next day
        closing_stock['creation_date__date'] = (pd.DatetimeIndex(
            closing_stock['creation_date__date']
            ) + pd.DateOffset(1)
        )
        orders['creation_date__date'] = pd.DatetimeIndex(orders['creation_date__date'])

        # Preparing Data For FMS Classification
        df = fmsc.prerequisite_for_abc_calculation(orders, closing_stock, days=average_days)
        # Running FMS Classification Custom For MB
        fmsdf = fmsc.abc(df, 'avg_%s_days' % average_days, abc_class_name='classification')
        rep = Replenishment(user_id)
        replenishment_master = rep.get_replenishment_data()
        current_stock = get_current_stock(sku_ids, zones=sa_zones)
        default_skus = list(set(sku_ids) - set(fmsdf.sku_id.to_list()))
        current_stock= self.get_sku_stock_data(current_stock, user_id, sku_ids)
        res = rep.replenishment(fmsdf, current_stock, replenishment_master,
            on_key='avg_%s_days' % average_days,
            default_skus=default_skus
        )
        rep.convert_df_to_model_and_store(res, on_key='avg_%s_days' % average_days)
        # Adding SKU Code and Description for reporting
        sku_master_df = pd.DataFrame(
            SKUMaster.objects.filter(id__in=sku_ids).values(
                "id", "sku_code", "sku_desc"
            ).annotate(sku_id=F("id"))
        )
        fmsdf = fmsdf.merge(sku_master_df, on=['sku_id'], how='left')
        #Sending Required report to Mail
        send_mail_attachments_for_reports(self.user, fmsdf, current_stock, res)


