# python imports
import datetime
import pandas as pd
import numpy as np
import traceback

# django imports
from django.db.models import F
from django.utils import timezone

# wms imports
from wms_base.models import User, UserAddresses
from wms_base.wms_utils import init_logger

# core imports
from core.models import SKUMaster
from core_operations.views.common.stock import StockDataMixIn

# inbound imports
from inbound.models import SKUSupplier

# inventory imports
from .stock_transfer_replenishment import STOReplenishment
from inventory.management.commands.min_max_based_auto_po import create_po_for_replenishment_qty
from .replenishment import StockDataMixIn

log = init_logger('logs/sto_stock_transfer_orders.log')


class ARSStockData(StockDataMixIn):
    """
    It collects data from various sources and merges them into a unified DataFrame
    for replenishment calculations.
    """

    def __init__(self, user_id, sku_ids):
        """
        Initialize the ARSStockData instance.

        Args:
            user_id (int): ID of the warehouse user
            sku_ids (list): List of SKU IDs to process
        """
        super().__init__(user_id, sku_ids)

    def merge_data(self, data=None):
        """
        Merge stock data from multiple sources into a unified DataFrame.

        This method collects data from various inventory sources and merges them
        to create a comprehensive view of stock status for replenishment calculations.

        Args:
            data (dict, optional): Pre-fetched data dictionary. If None, calls get_data()
                                  to retrieve it.

        Returns:
            pandas.DataFrame: Merged DataFrame with comprehensive stock data
        """
        if not data:
            data = self.get_data()

        # Start with pending putaway data
        df = data['pending_putaway_df']

        # Merge with other data sources
        df = df.merge(data['return_putaway_df'], on=['sku_code'], how='outer')
        df = df.merge(data['pull_to_locate_df'], on=['sku_code'], how='outer')
        df = df.merge(data['non_sellable_df'], on=['sku_code'], how='outer')
        df = df.merge(data['jo_pending_putaway_df'], on=['sku_code'], how='outer')
        df = df.merge(data['pending_grn_df'], on=['sku_code'], how='outer')

        # Fill missing values with zeros
        df = df.fillna(0)

        # Get SKU master data
        sku_master_df = pd.DataFrame(
            SKUMaster.objects.filter(id__in=self.sku_ids).values(
                "id", "sku_code"
            ).annotate(sku_id=F("id"))
        )
        # Merge with SKU master data
        df = df.merge(sku_master_df, on=['sku_code'], how='left')
        return df


class ARSReplenishment(STOReplenishment):
    """
    Implements the ARS-specific replenishment workflow for stock transfers.

    This class extends STOReplenushment to provide ARS-specific replenishment logic.
    It handles the workflow for creating stock transfer orders between warehouses
    based on replenishment requirements.

    Attributes:
        user_id (int): ID of the warehouse user
        suppliers (list): List of suppliers to filter by
        automate (bool): Flag indicating whether to automate the process
    """

    def __init__(self, user_id, suppliers=[], automate=False) -> None:
        """
        Initialize the ARSReplenushment instance.

        Args:
            user_id (int): ID of the warehouse user
            suppliers (list, optional): List of suppliers to filter by. Defaults to [].
            automate (bool, optional): Flag indicating whether to automate the process.
                                      Defaults to False.
        """
        super().__init__(user_id, suppliers, automate)

    def ars_replenishment_workflow(self, average_days):
        """
        Entry point for the ARS replenishment workflow.

        This method orchestrates the ARS replenishment process by delegating to
        the base replenishment workflow implementation.

        Args:
            average_days (int): Number of days to consider for average sales calculation

        Returns:
            pandas.DataFrame: Pure replenishment requirements
        """
        return self._execute_replenishment_workflow(average_days)

    def set_replenishment(self):
        """
        Execute replenishment by creating stock transfer orders.

        This method is called after replenishment requirements have been calculated.
        For main warehouses, it creates stock transfer orders to fulfill the
        replenishment requirements of child warehouses.

        Note: This implementation only handles main warehouses. For regular warehouses,
        the parent class implementation is used.
        """
        if self.main_warehouse:
            # Get pure requirements
            pure_req = self.pure_requirement

            # Group requirements by child SKU, child user, and SKU code
            pure_req = pure_req.groupby(by=['child_sku_id', 'child_user_id', 'sku_code']).max().reset_index()

            # Prepare STO purchase order payload
            sto_po_data = self.prepare_sto_po_payload(pure_req)

            # Create STO purchase orders
            self.create_sto_po_replenushment(self.user, sto_po_data)

    def get_replenushment_sku_ids(self, user_id):
        """
        Retrieve SKU IDs eligible for replenishment.

        This method delegates to the parent class implementation.

        Args:
            user_id (int): ID of the warehouse user

        Returns:
            list: List of eligible SKU IDs
        """
        return super().get_replenushment_sku_ids(user_id)

    def get_sku_classification(self, users):
        """
        Retrieve SKU classification data for replenishment calculations.

        This method delegates to the parent class implementation.

        Args:
            users (list): List of user IDs

        Returns:
            QuerySet: SKU classification records
        """
        return super().get_sku_classification(users)

    def get_sku_stock_data(self, current_stock_df, user_id, sku_ids):
        """
        Retrieve and process stock data for replenishment calculations.

        This method uses ARSStockData to get comprehensive stock data and
        adjusts available quantities based on pending operations.

        Args:
            current_stock_df (pandas.DataFrame): DataFrame with current stock data
            user_id (int): ID of the warehouse user
            sku_ids (list): List of SKU IDs to process

        Returns:
            pandas.DataFrame: Updated stock DataFrame with adjusted quantities
        """
        # Get stock data using ARSStockData
        stock_data_df = ARSStockData(user_id, sku_ids).merge_data()

        if not stock_data_df.empty:
            # Merge with current stock data
            current_stock_df = stock_data_df.merge(current_stock_df, on=['sku_id'], how='left')

            # Adjust available quantities based on pending operations
            current_stock_df['available_qty'] = current_stock_df['available_qty'] - (
                current_stock_df['pending_putaway_qty'] + current_stock_df['return_putaway_qty']
            )

        return current_stock_df

    def merge_with_master_sku(self, child_whs_full_df):
        """
        Merge child warehouse data with master SKU data.

        This method retrieves supplier data for SKUs and merges it with child
        warehouse data. It also calculates lead time and expected delivery dates.

        Args:
            child_whs_full_df (pandas.DataFrame): DataFrame with child warehouse data

        Returns:
            pandas.DataFrame: Enhanced DataFrame with supplier and delivery information
        """
        # Retrieve supplier data for SKUs
        sku_suppliers_df = pd.DataFrame(SKUSupplier.objects.filter(
            supplier__id__in=child_whs_full_df['supplier_id'],
            sku__id__in=child_whs_full_df['child_sku_id']
        ).values('lead_time', 'sku__id', 'supplier__id'))

        # Rename columns to match child warehouse data
        sku_suppliers_df = sku_suppliers_df.rename(columns={
            'sku__id': 'child_sku_id',
            'supplier__id': 'supplier_id'
        })

        # Merge with child warehouse data
        if not sku_suppliers_df.empty:
            child_whs_full_df = child_whs_full_df.merge(sku_suppliers_df, on=['child_sku_id', 'supplier_id'], how='left')
        else:
            child_whs_full_df['lead_time'] = 0

        # Fill missing lead times with zero
        child_whs_full_df['lead_time'] = child_whs_full_df['lead_time'].fillna(0)

        # Calculate expected delivery date based on lead time
        child_whs_full_df['exp_delivery_date'] = child_whs_full_df['lead_time'].apply(
            lambda lead_time: (timezone.now().today() + datetime.timedelta(days=lead_time)).strftime("%m/%d/%Y"))

        return child_whs_full_df

    def prepare_sto_po_payload(self, pure_req):
        """
        Prepare payload for stock transfer purchase order creation.

        This method groups replenishment requirements by supplier, user, and
        expected delivery date, and formats them for purchase order creation.

        Args:
            pure_req (pandas.DataFrame): DataFrame with pure replenishment requirements

        Returns:
            dict: Dictionary with purchase order data by supplier
        """
        po_creation_data = {}

        # Group requirements by supplier, user, and expected delivery date
        for (supplier_id, user, exp_delivery_date), group in pure_req.groupby(['po_supplier_id', 'child_user_id', 'exp_delivery_date']):
            # Format items for purchase order
            items = group[['sku_code', 'replenushment_qty']].rename(columns={
                'sku_code': 'sku',
                'replenushment_qty': 'order_quantity'
            }).to_dict(orient='records')

            # Get additional data from group
            exp_delivery_date = group['exp_delivery_date'].iloc[0]
            supplier_type = group['supplier_type'].iloc[0]

            # Create unique key for supplier
            po_supplier_uniq_key = f'{supplier_id}_:{user}_:{exp_delivery_date}'

            # Add to purchase order data
            po_creation_data[po_supplier_uniq_key] = {
                'exp_delivery_date': exp_delivery_date,
                'items': items,
                'supplier_type': supplier_type
            }

        return po_creation_data

    def create_sto_po_replenushment(self, main_user, po_creation_data):
        """
        Create stock transfer purchase orders for replenishment.

        This method creates purchase orders for child warehouses based on the
        replenishment requirements. It retrieves shipping addresses and creates
        purchase orders for each supplier.

        Args:
            main_user (User): Main warehouse user
            po_creation_data (dict): Purchase order creation data by supplier
        """
        # Get child warehouses
        users = self.children_ids or []
        ch_warehouses = User.objects.filter(id__in=users)

        # Process each child warehouse
        for ch_warehouse in ch_warehouses:
            # Get shipping address
            try:
                user_address = UserAddresses.objects.filter(user=ch_warehouse.id)[0]
            except IndexError:
                log.error(f"No address found for warehouse {ch_warehouse.username}")
                continue

            if user_address:
                # Format shipping address
                ship_to = ('%s %s, %s' % (user_address.address_name, user_address.address, user_address.pincode))

                # Process each purchase order
                for key, po_data in po_creation_data.items():
                    try:
                        # Parse key components
                        supplier_id, user, exp_delivery_date = key.split('_:')

                        # Skip if not for this warehouse
                        if int(user) != ch_warehouse.id:
                            log.info(f"Skipping PO for {ch_warehouse.username} for {key}")
                            continue

                        # Create purchase order
                        response_dict = create_po_for_replenishment_qty(ch_warehouse, {supplier_id: po_data}, ship_to)

                        # Log result
                        if response_dict:
                            log.error(f"PO failed for {ch_warehouse.username} for {po_data} as {response_dict}")
                        else:
                            log.info(f"PO created for {ch_warehouse.username} for {key}")

                    except Exception as e:
                        # Log error details
                        log.debug(traceback.format_exc())
                        log.error(f"Error in creating PO for {ch_warehouse.username} for {key}: {str(e)}")
