# python imports
import datetime
import pandas as pd
import numpy as np

# django imports
from django.db.models import Sum, F
from django.utils import timezone

# core imports
from core.models import SKUMaster

# inbound imports
from inbound.models import SKUSupplier, SkuClassification

# outbound imports
from outbound.models import OrderDetail

# inventory imports
from inventory.models import StockDetail
from .base import ReplenishmentMixin


class STOReplenishment(ReplenishmentMixin):
    def __init__(self, user_id, suppliers=[], automate=False) -> None:
        self.suppliers = suppliers
        super().__init__(user_id, automate)

    def get_bulk_stock(self, sku_ids):
        bulk_zones = self.misc_values_dict.get('bulk_zones_list', '') or []
        bulk_stock_objs = StockDetail.objects.filter(
            sku_id__in=sku_ids, 
            quantity__gt=0
        )
        if bulk_zones:
            bulk_stock_objs = bulk_stock_objs.filter(
                location__zone__zone__in=bulk_zones
            )
        bulk_stock = pd.DataFrame(bulk_stock_objs.values(
                'batch_detail__buy_price',
                'batch_detail__mrp',
                'batch_detail__weight'
            ).annotate(
                stock_id=F('id'),
                ba_sku_id=F('sku_id'),
                stock_quantity=F('quantity')
            ))
        if bulk_stock.empty:
            return False, "No Stock In Bulk Area"
        return True, bulk_stock

    def _execute_replenishment_workflow(self, average_days):
        user_id = self.user_id
        self.set_multi_wh_check()
        if not self.main_warehouse:
            self.run_store_replenushment_workflow(user_id, average_days, True)
        self.prepare_replenishment()
        if not self.pure_requirement.empty:
            self.set_replenishment()
        return self.pure_requirement

    def st_replenishment_workflow(self, average_days):
        return self._execute_replenishment_workflow(average_days)

    def get_replenushment_sku_ids(self, user_id):
        sku_ids = list(SKUMaster.objects.filter(user=user_id, status=1).values_list('id', flat=True))
        if self.suppliers:
            sku_ids = list(SKUSupplier.objects.filter(
                sku__in=sku_ids,
                supplier__name__in=self.suppliers,
                supplier__status=1
            ).values_list(
                'sku_id', flat=True
            ).distinct())
        return sku_ids

    def get_sku_classification(self, users):
        return SkuClassification.objects.filter(
                sku__user__in=users,
                creation_date__gte=timezone.now().date(),
                replenushment_qty__gt=0)

    def set_replenishment(self):
        bulk_move_intentory = False
        if self.main_warehouse:
            bulk_move_intentory = True
        return super().set_replenishment(bulk_move_intentory=bulk_move_intentory)

    def get_open_orders(self, user_id, sku_ids):
        open_orders_df= pd.DataFrame(
            OrderDetail.objects.filter(
                user=user_id,
                quantity__gt=0,
                status=1,
                sku__in=sku_ids
            ).values(
                'sku__id'
            ).annotate(
                open_order_qty=Sum('quantity')
            )
        )
        open_orders_df.rename(columns={'sku__id': 'sku_id'}, inplace=True)
        return open_orders_df