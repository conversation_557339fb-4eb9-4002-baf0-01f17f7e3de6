import ast
from datetime import datetime
from abc import ABC, abstractmethod

from django.core.management import BaseCommand, CommandError

#wms base
from wms_base.wms_utils import init_logger
from wms_base.models import User

#core imports
from core.models import MiscDetail

log = init_logger('logs/base_stock_transfer_orders.log')


class BaseStockTransferCommand(BaseCommand, ABC):
    """
    Base class for Stock Transfer Order commands
    
    This abstract base class provides common functionality for both
    auto_stock_transfer_orders.py and ars_stock_transfer_orders.py
    """
    
    help = "Base Stock Transfer Orders"
    
    def add_arguments(self, parser):
        """
        Define command line arguments
        """
        parser.add_argument('input_dict', type=str, default=None)
    
    def handle(self, *args, **options):
        """
        Entry point for the command
        
        Args:
            *args: Variable length argument list
            **options: Arbitrary keyword arguments including 'input_dict'
        """
        try:
            extra_data = ast.literal_eval(options['extra_params'])
            self.warehouses = ars_warehouses = extra_data.get('ars_warehouses', [])
            self.user_objs = User.objects.filter(username__in=ars_warehouses)
            central_warehouses = extra_data.get('central_warehouses', {})
            log.info('ARS Calculation started for warehouses: {0}, time: {1}'.format(ars_warehouses, datetime.datetime.now()))
        except Exception as e:
            import traceback
            log.debug(traceback.format_exc())
            log.info('ARS Calculation failed for warehouses: {0}, error: {1}'.format(ars_warehouses, str(e)))
            raise CommandError('ARS Calculation failed for warehouses: {0}, error: {1}'.format(ars_warehouses, str(e)))

        # Extract configuration from input dictionary
        self.average_days = extra_data.get('avarage_days', 0)
        self.calculation_type = extra_data.get('calculation_type', "ars")
        self.common_emails = extra_data.get('common_emails', [])  # Common emails for all warehouses
        self.warehouse_emails = extra_data.get('warehouse_emails', {})  # Warehouse specific emails
        self.user_ids = list(self.user_objs.values_list('id', flat=True))
        self.suppliers = extra_data.get('suppliers', [])

        # Initialize logger
        self.log = self.initialize_logger()
    
        # Get configuration values and process each warehouse
        self.get_misc_values()
        for each_user in self.user_objs:
            self.user = each_user
            self.username = str(each_user.username)
            self.process_stock_transfer_orders()

    @abstractmethod
    def initialize_logger(self):
        """
        Initialize the logger for the specific command
        
        Returns:
            Logger instance
        """
        pass

    def process_stock_transfer_orders(self):
        """
        Process stock transfer orders for a warehouse
        
        This method orchestrates the stock transfer process based on the
        configured calculation type.
        """
        self.users = [self.user]
        self.emails_list = self.common_emails + self.warehouse_emails.get(self.user.username, [])

        # Log start of process
        command_name = self.get_command_name()
        self.log.info(f"{self.username} {command_name}: Start time - {datetime.now()} Calculation Type = {self.calculation_type}")

        # Execute appropriate calculation method
        if str(self.calculation_type).lower() == "ars":
            self.abc_calculation()
        else:
            raise NotImplementedError("Calculation type not implemented")

        # Log completion of process
        self.log.info(f"{self.username} {command_name} Completed for {self.username} at {datetime.now()}")
    
    def get_misc_values(self):
        """
        Get all needed misc values from the database
        
        Retrieves configuration values from MiscDetail table and stores them
        in self.switch_values dictionary.
        """
        self.switch_values = {}
        misc_types = [
            'bulk_zones_list',
            'pick_zones_list'
        ]
        # Query MiscDetail for configuration values
        misc_data = list(MiscDetail.objects.filter(
            user__in=self.user_ids, misc_type__in=misc_types).values('user', 'misc_type', 'misc_value'))
        
        # Store values in switch_values dictionary
        for misc in misc_data:
            user_id = misc.get('user')
            self.switch_values.setdefault(user_id, {})
            self.switch_values[user_id][misc.get('misc_type', '')] = misc.get('misc_value', '')
    
    @abstractmethod
    def abc_calculation(self):
        """
        Implement ABC classification-based inventory movement
        
        This method should be implemented by subclasses to handle the
        specific replenishment logic.
        """
        pass

    @abstractmethod
    def get_command_name(self):
        """
        Get the name of the command for logging purposes
        
        Returns:
            str: Command name
        """
        pass

    def process_abc_calculation(self, replenishment_cls):
        sto_rep = replenishment_cls(user_id=self.user.id, automate=True, suppliers=self.suppliers)
        if sto_rep.main_warehouse:
            for ars_user_id in sto_rep.children_ids:
                try:
                    child_sto_rep = replenishment_cls(user_id=ars_user_id,automate=True, suppliers=self.suppliers)
                    child_sto_rep._execute_replenishment_workflow(self.average_days)
                except Exception as err:
                    self.log.error(f"Error in STO for child user {ars_user_id}: {str(err)}")
        sto_rep._execute_replenishment_workflow(self.average_days)
