# python imports
import pandas as pd

# django imports
from django.db.models import Sum, Q, F

# inventory imports
from inventory.models import StockDetail

# inbound imports
from inbound.models import POLocation, PurchaseOrder


class StockDataMixIn():
    def __init__(self, user_id, sku_ids):
        self.user_id = user_id
        self.sku_ids = sku_ids

    def get_data(self):
        self.set_pending_putaway_quantity()
        self.set_return_putaway_quantity()
        self.set_pull_to_locate_quantity()
        self.set_non_sellable_quantity()
        self.set_jo_pending_putaway_quantity()
        self.set_pending_grn_quantity()

        return {
            'pending_putaway_df': self.pen_pw_df,
            'return_putaway_df': self.return_pw_df,
            'pull_to_locate_df': self.pull_to_locate_df,
            'non_sellable_df': self.non_sellable_df,
            'jo_pending_putaway_df': self.jo_pending_putaway_df,
            'pending_grn_df': self.pending_grn_df
        }

    def set_pending_putaway_quantity(self, pd_df=True):
        pol_objs = POLocation.objects.filter(
                status=1,
                location__zone__user=self.user_id,
                purchase_order__isnull=False)
        if self.sku_ids:
            pol_objs = pol_objs.filter(purchase_order__open_po__sku__in=self.sku_ids)

        pen_pw_data =list(pol_objs.values_list(
            'purchase_order__open_po__sku__sku_code').distinct().annotate(pending_putaway_qty=Sum('quantity'))
        )
        self.pen_pw_df = pd.DataFrame(pen_pw_data, columns=['sku_code', 'pending_putaway_qty'])

    def set_return_putaway_quantity(self):
        pol_objs = POLocation.objects.filter(
                status=1,
                location__zone__user=self.user_id,
                seller_po_summary__sales_return_batch__isnull=False)
        if self.sku_ids:
            pol_objs = pol_objs.filter(seller_po_summary__sales_return_batch__sales_return_sku__sku__in=self.sku_ids)

        return_pw_data = list(pol_objs.values_list(
            'seller_po_summary__sales_return_batch__sales_return_sku__sku__sku_code').distinct().annotate(return_putaway_qty=Sum('quantity'))
        )
        self.return_pw_df = pd.DataFrame(return_pw_data, columns=['sku_code', 'return_putaway_qty'])

    def set_pull_to_locate_quantity(self):
        pol_objs = POLocation.objects.filter(
                picklist__isnull=False,
                status=1,
                sku__user=self.user_id)
        if self.sku_ids:
            pol_objs = pol_objs.filter(sku__in=self.sku_ids)

        pull_to_locate_data =list(pol_objs.values_list(
            'sku__sku_code').distinct().annotate(pull_to_locate_qty=Sum('quantity'))
        )
        self.pull_to_locate_df = pd.DataFrame(pull_to_locate_data, columns=['sku_code', 'pull_to_locate_qty'])

    def set_non_sellable_quantity(self):
        stock_objs = StockDetail.objects.filter(
                Q(location__zone__segregation="non_sellable") |
                Q(location__zone__zone__in =['DAMAGED_ZONE','QC_ZONE']),
                quantity__gt=0,
                sku__user=self.user_id)
        if self.sku_ids:
            stock_objs = stock_objs.filter(sku__in=self.sku_ids)

        non_sellable_data = list(stock_objs.values_list(
            'sku__sku_code').distinct().annotate(non_sellable_qty=Sum('quantity'))
        )
        self.non_sellable_df = pd.DataFrame(non_sellable_data, columns=['sku_code', 'non_sellable_qty'])

    def set_jo_pending_putaway_quantity(self):
        pol_objs = POLocation.objects.filter(
                status=1,
                location__zone__user=self.user_id,
                jo_grn__isnull=False)
        if self.sku_ids:
            pol_objs = pol_objs.filter(job_order__product_code__in=self.sku_ids)

        jo_pending_putaway_data = list(pol_objs.values_list(
            'job_order__product_code__sku_code').distinct().annotate(jo_pending_putaway_qty=Sum('quantity'))
        )
        self.jo_pending_putaway_df = pd.DataFrame(jo_pending_putaway_data, columns=['sku_code', 'jo_pending_putaway_qty'])

    def set_pending_grn_quantity(self):
        po_objs = PurchaseOrder.objects.filter(
                open_po__sku__user=self.user_id,
                received_quantity__lt=F('open_po__order_quantity')
            ).exclude(status='location-assigned')
        if self.sku_ids:
            po_objs = po_objs.filter(open_po__sku__in=self.sku_ids)

        pending_grn_data =list(po_objs.values_list(
            'open_po__sku__sku_code').distinct().annotate(pending_grn_qty=Sum(F('open_po__order_quantity')-F('received_quantity')))
        )
        self.pending_grn_df = pd.DataFrame(pending_grn_data, columns=['sku_code', 'pending_grn_qty'])