from django.db.models import Index

class UpperIndex(Index):
   def create_sql(self, model, schema_editor, using='', **kwargs):
       statement = super().create_sql(
           model, schema_editor, using, **kwargs
       )
       quote_name = statement.parts['columns'].quote_name

       def upper_quoted(column):
           return 'UPPER({0})'.format(quote_name(column))

       statement.parts['columns'].quote_name = upper_quoted
       return statement