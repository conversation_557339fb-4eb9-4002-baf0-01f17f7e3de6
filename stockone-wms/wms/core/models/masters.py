from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey

from django.core.validators import MinValueValidator, MaxValueValidator
from wms_base.models import BaseModel, CompanyMaster, TenantBaseModel, User, POChoices
from wms_base.wms_utils import ForeignKey
from datetime import date, timedelta
from django.db.models import Q, F, Min, Max
from wms_base.choices import QC_CATAGORIES

from core.models.helpers import UpperIndex

PERCENTAGE_VALIDATOR = [MinValueValidator(0), MaxValueValidator(100)]

class PalletDetail(TenantBaseModel):
    
    user = models.PositiveIntegerField()
    pallet_code = models.CharField(max_length=64)
    quantity = models.FloatField(default=0)
    status = models.IntegerField(default=1)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'PALLET_DETAIL'
        indexes = [
            models.Index(fields=['user', 'pallet_code']),
            models.Index(fields=['user', 'pallet_code', 'quantity']),
        ]

        
class AddressMaster(TenantBaseModel):
    
    address_line_one = models.TextField(max_length=512, default='', null=True)
    address_line_two = models.TextField(max_length=512, default='', null=True)
    city = models.CharField(max_length=64, default='', null=True)
    state = models.CharField(max_length=64, default='', null=True)
    pincode = models.IntegerField(blank=True, null=True)
    country = models.CharField(max_length=64, null=True)
    zip_code = models.CharField(max_length=10, null=True)
    latitude = models.CharField(max_length=64,blank=True, null=True)
    longitude = models.CharField(max_length=64,blank=True, null=True)
    user = ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=64,blank=True, null=True)
    phone_number = models.CharField(max_length=32, blank=True, null=True)
    address_type = models.CharField(max_length=16, default='', null=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)


class UserAttributes(TenantBaseModel):
    user = ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True)
    usage = models.CharField(max_length=64, default='extra-attrs')
    attribute_model = models.CharField(max_length=32, default='')
    show_name = models.TextField(null=True, blank=True)
    attribute_name = models.CharField(max_length=64, default='')
    attribute_type = models.CharField(max_length=64, default='')
    attribute_values = models.TextField()
    attribute_order = models.IntegerField(default=0)
    is_mandatory = models.BooleanField(default=False)
    status = models.IntegerField(default=1)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    json_data = models.JSONField(null=True,blank=True)

    class Meta:
        db_table = 'USER_ATTRIBUTES'
        unique_together = ('user', 'attribute_model', 'attribute_name')

class FormFieldValue(TenantBaseModel):
    field = models.ForeignKey(UserAttributes, on_delete=models.CASCADE)
    value = models.CharField(max_length=256, default='')

    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    extra_flag = models.CharField(max_length=64, default='')

    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'FORM_FIELD_VALUE'


class SKUMaster(TenantBaseModel):
    user = models.PositiveIntegerField(db_index=True)
    sku_code = models.CharField(max_length=128)
    wms_code = models.CharField(max_length=128)
    sku_desc = models.CharField(max_length=350, default='')
    sku_group = models.CharField(max_length=64, default='')
    sku_type = models.CharField(max_length=64, default='')
    sku_category = models.CharField(max_length=128, default='')
    sku_class = models.CharField(max_length=64, default='')
    sku_brand = models.CharField(max_length=64, default='')
    style_name = models.CharField(max_length=256, default='')
    sku_size = models.CharField(max_length=64, default='')
    product_type = models.CharField(max_length=64, default='')
    zone = ForeignKey('inventory.ZoneMaster',on_delete=models.CASCADE, null=True, blank=True, default=None)
    seller = ForeignKey('inbound.SupplierMaster',on_delete=models.CASCADE, null=True, blank=True, default=None)
    pick_and_sort = models.PositiveIntegerField(default=0)
    threshold_quantity = models.FloatField(default=0)
    max_norm_quantity = models.FloatField(default=0)
    online_percentage = models.PositiveIntegerField(default=0)
    discount_percentage = models.PositiveIntegerField(default=0)
    price = models.FloatField(default=0)
    cost_price = models.FloatField(default=0)
    mrp = models.FloatField(default=0)
    image_url = models.URLField(default='')
    qc_check = models.IntegerField(default=1)
    scan_picking = models.IntegerField(default=0)
    is_barcode_required = models.BooleanField(default=False)
    status = models.IntegerField(default=1)
    relation_type = models.CharField(max_length=32, default='')
    measurement_type = models.CharField(max_length=32, default='')
    sale_through = models.CharField(max_length=32, default='')
    mix_sku = models.CharField(max_length=32, default='', db_index=True)
    color = models.CharField(max_length=64, default='')
    ean_number = models.CharField(max_length=512, default='')
    load_unit_handle = models.CharField(max_length=32, default='unit', db_index=True)
    hsn_code = models.CharField(max_length=20, db_index=True, default='')
    sequence = models.IntegerField(default=0)
    sub_category = models.CharField(max_length=64, default='')
    pick_group = models.CharField(max_length=64, default='')
    shelf_life = models.FloatField(default=0)
    customer_shelf_life = models.DurationField(default=timedelta(days=0))
    minimum_shelf_life = models.DurationField(default=timedelta(days=0))
    youtube_url = models.CharField(max_length=64, default='')
    enable_serial_based = models.IntegerField(default=0)
    block_options = models.CharField(max_length=5, default='')
    batch_based = models.IntegerField(default=0)
    dispensing_enabled = models.IntegerField(default=0)
    gl_code = models.PositiveIntegerField(default=0)
    average_price = models.FloatField(default=0)
    average_price_rt = models.FloatField(default=0)
    length = models.CharField(max_length=64, default=0)
    breadth = models.CharField(max_length=64, default=0)
    weight = models.CharField(max_length=64, default=0)
    height = models.CharField(max_length=64, default=0)
    volume = models.FloatField(default=0)
    article_no = models.CharField(max_length=128, default='')
    sku_reference = models.CharField(max_length=128, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    json_data = models.JSONField(null=True,blank=True)
    qc_eligible = models.BooleanField(default=False)
    qc_category =  models.CharField(max_length=64, choices=QC_CATAGORIES, default='')
    sample_percentage = models.FloatField(default=0, validators=PERCENTAGE_VALIDATOR)
    receipt_tolerance = models.FloatField(default=0)
    make_or_buy = models.IntegerField(
        choices=POChoices.choices,
        default=POChoices.DEFAULT
    )
    allow_price_override = models.IntegerField(default=0)
    invoice_group = models.CharField(max_length=64, default='')
    mandate_scan = models.BooleanField(default=False)

    class Meta:
        db_table = 'SKU_MASTER'
        unique_together = ('user', 'sku_code', 'wms_code')
        indexes = [
            UpperIndex(fields=['sku_code'], name='sku_code_upper_index'),
            models.Index(fields=['user', 'sku_code', 'wms_code']),
            models.Index(fields=['user', 'sku_code']),
            models.Index(fields=['sku_code']),
        ]

    def __str__(self):
        return str(self.sku_code)

    def natural_key(self):
        zone = ''
        if self.zone:
            zone = self.zone.zone
        return {'id': self.id, 'sku_code': self.sku_code, 'wms_code': self.wms_code,
                'sku_desc': self.sku_desc, 'sku_group': self.sku_group, 'sku_type': self.sku_type,
                'sku_category': self.sku_category, 'sku_class': self.sku_class, 'zone': zone,
                'threshold_quantity': self.threshold_quantity, 'online_percentage': self.online_percentage,
                'discount_percentage': self.discount_percentage, 'price': self.price, 'image_url': self.image_url,
                'qc_check': self.qc_check, 'status': self.status, 'relation_type': self.relation_type}

class SKUFields(TenantBaseModel):
    sku = ForeignKey(SKUMaster,on_delete=models.CASCADE)
    field_id = models.PositiveIntegerField(default=0)
    field_type = models.CharField(max_length=128, default='')
    field_value = models.CharField(max_length=128, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SKU_FIELDS'

class SKURelation(TenantBaseModel):
    
    parent_sku = ForeignKey(SKUMaster,on_delete=models.CASCADE, blank=True, null=True, related_name='parent_sku')
    member_sku = ForeignKey(SKUMaster,on_delete=models.CASCADE, blank=True, null=True, related_name='member_sku')
    quantity = models.FloatField(default=0)
    relation_type = models.CharField(max_length=64, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SKU_RELATION'
        unique_together = ('parent_sku', 'member_sku', 'relation_type')
        indexes = [
            models.Index(fields=['parent_sku', 'member_sku', 'relation_type']),
            models.Index(fields=['parent_sku', 'member_sku']),
            models.Index(fields=['parent_sku', 'relation_type']),
        ]

    def __str__(self):
        return '%s: %s || %s' % (self.relation_type, self.parent_sku, self.member_sku)


class SKUAttributes(TenantBaseModel):
    sku = ForeignKey(SKUMaster,on_delete=models.CASCADE, blank=True, null=True)
    attribute_name = models.CharField(max_length=64, default='', db_index=True)
    attribute_value = models.CharField(max_length=128, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SKU_ATTRIBUTES'
        indexes = [
            models.Index(fields=['sku', 'attribute_name']),
            models.Index(fields=['attribute_name','attribute_value']),
        ]

    def __str__(self):
        return str(self.sku.sku_code) + '-' + str(self.attribute_name)

class TaxMaster(TenantBaseModel):
    
    user = ForeignKey(User,on_delete=models.CASCADE, null=True, blank=True, default=None)
    product_type = models.CharField(max_length=64, default='', db_index=True)
    inter_state = models.IntegerField(default=0)
    cgst_tax = models.FloatField(default=0)
    sgst_tax = models.FloatField(default=0)
    igst_tax = models.FloatField(default=0)
    cess_tax = models.FloatField(default=0)
    utgst_tax = models.FloatField(default=0)
    apmc_tax = models.FloatField(default=0)
    min_amt = models.FloatField(default=0)
    max_amt = models.FloatField(default=0)
    reference_id = models.CharField(max_length=64, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    json_data = models.JSONField(null=True, blank=True)
    class Meta:
        db_table = 'TAX_MASTER'
        indexes = [
            models.Index(fields=['user', 'product_type', 'inter_state']),
            models.Index(fields=['cgst_tax', 'sgst_tax', 'igst_tax', 'cess_tax', 'user']),
            models.Index(fields=['user', 'product_type']),
        ]

    def json(self):
        return {
            'id': self.id,
            'product_type': self.product_type,
            'inter_state': self.inter_state,
            'cgst_tax': self.cgst_tax,
            'sgst_tax': self.sgst_tax,
            'igst_tax': self.igst_tax,
            'cess_tax': self.cess_tax,
            'utgst_tax': self.utgst_tax,
            'apmc_tax': self.apmc_tax,
            'min_amt': self.min_amt,
            'max_amt': self.max_amt,
            'user_id': self.user.id
        }

class HSNMaster(TenantBaseModel):
    warehouse = ForeignKey(User,on_delete=models.CASCADE, null=True, blank=True, default=None,db_index=True)
    product_type = models.CharField(max_length=128, default='')
    hsn_code = models.CharField(max_length=128, default='')
    hsn_description = models.CharField(max_length=128, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'HSN_MASTER'
        unique_together = ('warehouse', 'hsn_code')
        indexes = [
            models.Index(fields=['warehouse', 'hsn_code']),
        ]

    def __str__(self):
        return str(self.id)

class EANNumbers(TenantBaseModel):
    
    ean_number = models.CharField(max_length=512, default='')
    sku = ForeignKey(SKUMaster,on_delete=models.CASCADE)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'EAN_NUMBERS'
        unique_together = ('ean_number', 'sku')
        indexes = [
            models.Index(fields=['sku', 'ean_number']),
            models.Index(fields=['ean_number']),
        ]

class SKUImages(TenantBaseModel):
    sku = ForeignKey(SKUMaster,on_delete=models.CASCADE)
    image_url = models.CharField(max_length=256, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SKU_IMAGES'
        unique_together = ('sku', 'image_url')

class SKUGroups(TenantBaseModel):
    
    user = models.PositiveIntegerField()
    group = models.CharField(max_length=60, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SKU_GROUPS'
        unique_together = ('user', 'group')

class UOMMaster(BaseModel):
    
    company = ForeignKey(CompanyMaster,on_delete=models.CASCADE)
    name = models.CharField(max_length=128, default='')
    sku_code = models.CharField(max_length=128, default='')
    base_uom = models.CharField(max_length=32, default='')
    uom_type = models.CharField(max_length=64, default='')
    uom = models.CharField(max_length=64, default='')
    conversion = models.FloatField(default=0)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'UOM_MASTER'
        unique_together = ('company', 'sku_code', 'base_uom', 'uom_type', 'uom', 'conversion')

    def __str__(self):
        return '%s-%s' % (self.company, self.name)

class SizeMaster(TenantBaseModel):
    user = models.PositiveIntegerField()
    size_name = models.CharField(max_length=64, default='')
    size_value = models.TextField()
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SIZE_MASTER'
        unique_together = ('user', 'size_name')

class CartonTypes(TenantBaseModel):
    
    user = ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True)
    area = models.CharField(max_length=32,null=True, blank=True)
    carton_type = models.CharField(max_length=32,null=True, blank=True)
    carton_prefix = models.CharField(max_length=32,null=True, blank=True)
    length = models.FloatField(default=0)
    width = models.FloatField(default=0)
    height = models.FloatField(default=0)
    weight = models.FloatField(default=0)
    json_data = models.JSONField(null=True, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'CARTON_TYPES'
        unique_together = ('area', 'carton_type', 'user')

class AuthorizedBins(TenantBaseModel):
    
    user = ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True)
    carton_types = ForeignKey(CartonTypes,on_delete=models.CASCADE, blank=True, null=True)
    bin_number = models.CharField(max_length=64)
    status = models.PositiveIntegerField(default=0)
    prefix = models.CharField(max_length=32, null=True, blank=True)
    bin_type = models.CharField(max_length=32, default='general')
    create_user = models.CharField(max_length=64, null=True, blank=True)
    json_data = models.JSONField(null=True, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'AUTHORIZED_BINS'
        indexes = [
            models.Index(fields=['bin_number', 'status', 'user']),
            models.Index(fields=['bin_number', 'user']),
        ]

class UOMDetail(BaseModel):
    
    company = ForeignKey(CompanyMaster,on_delete=models.CASCADE)
    uom_code = models.CharField(max_length=128, default='')
    uom_description = models.CharField(max_length=128, default='')
    uom_class = models.CharField(max_length=64, default='')
    conversion_factor = models.FloatField(default=0)
    decimals = models.DecimalField(max_digits=50, decimal_places=0,default=0)
    base_uom = models.BooleanField(default=False)
    status = models.IntegerField(default=1)
    created_by = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True, related_name='uom_created_by')
    updated_by = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True, related_name='uom_updated_by')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'UOM_Detail'
        unique_together = ('company','uom_code','status')

    def __str__(self):
        return '%s-%s' % (self.company, self.uom_code)

class CurrencyExchangeMaster(TenantBaseModel):
    
    warehouse = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    from_currency = models.CharField(max_length=5, blank=True, null=True)
    to_currency = models.CharField(max_length=5, blank=True, null=True)
    exchange_rate = models.FloatField(default=1)
    start_date = models.DateTimeField(blank=True, null=True)
    end_date = models.DateTimeField(blank=True, null=True)
    created_by = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True, related_name='created_by')
    updated_by = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True, related_name='updated_by')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'CURRENCY_EXCHANGE_MASTER'
        indexes = [
            models.Index(fields=['warehouse', 'from_currency', 'to_currency']),
        ]

    
class TermsAndConditions(TenantBaseModel):
    warehouse = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True) 
    document_type = models.CharField(max_length=64, default='')
    reference = models.CharField(max_length=64, default='')
    terms_and_conditions = models.TextField(blank=True, null=True)
    status = models.IntegerField(default=1)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'TERMS_AND_CONDITIONS'
        unique_together = ('warehouse', 'document_type', 'reference')
        indexes = [
            models.Index(fields=['warehouse', 'document_type', 'reference', 'status']),
            models.Index(fields=['warehouse', 'document_type']),
        ]
    
    def __str__(self):
        return str(self.document_type) + "_" + str(self.reference)

class SKUWAC(TenantBaseModel):
    warehouse = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    sku = ForeignKey(SKUMaster,on_delete=models.CASCADE)
    average_price = models.FloatField(default=0)
    average_price_rt = models.FloatField(default=0)
    json_data = models.JSONField(null=True, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SKU_WAC'
        unique_together = ('warehouse', 'sku')