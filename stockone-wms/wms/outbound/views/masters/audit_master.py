
import json
import pytz
import traceback
from django.utils import timezone
from copy import deepcopy

# Django imports
from django.http import JsonResponse

# wms imports
from wms_base.wms_utils import init_logger

# outbound imports
from outbound.models import AuditMaster

from outbound.views.audit.constants import MANDATORY_FIELDS, VALID_REFERENCE_TYPES, VALID_AUDIT_TYPES

from core_operations.views.common.main import (
    WMSListView, get_local_date_known_timezone
)


log = init_logger('logs/audit_master.log')

class AuditMasterViewSet(WMSListView):
    """
    API endpoint for managing audit configurations.
    """
    
    def get_request_data(self):
        self.errors = []
        self.reference_type = self.request_data.get('reference_type', '')
        self.reference_number = self.request_data.get('reference_number', '')
        self.audit_type = self.request_data.get('audit_type', '')
        self.audit_value = self.request_data.get('audit_value', 0)
        self.counter_reset_days = self.request_data.get('counter_reset_days', 1)
        self.status = self.request_data.get('status', 1)
    
    def validate_request_data(self):
        for mandatory_field in MANDATORY_FIELDS:
            if mandatory_field not in self.request_data:
                self.errors.append(f'{mandatory_field} is mandatory')
                return
        
        if self.reference_type not in VALID_REFERENCE_TYPES:
            self.errors.append('Invalid reference type')
            return
        
        if self.audit_type not in VALID_AUDIT_TYPES:
            self.errors.append('Invalid audit type')
            return
    
    def insert_audit_configuration(self):
        # Check if a configuration already exists for this reference
        existing_config = AuditMaster.objects.filter(
            warehouse=self.warehouse,
            reference_type=self.reference_type,
            reference_number=self.reference_number
        )
        if existing_config.exists():
            self.errors.append('Audit Configuration already exists for this reference')
        else:
            AuditMaster.objects.create(
                warehouse=self.warehouse,
                reference_type=self.reference_type,
                reference_number=self.reference_number,
                audit_type=self.audit_type,
                audit_value=self.audit_value,
                counter_reset_days=self.counter_reset_days,
                status=self.status,
                account_id=self.warehouse.userprofile.id,
                created_by_id=self.user.id,
                updated_by_id=self.user.id
            )
    
    def update_audit_configuration(self):
        # Check if a configuration exists for this reference
        existing_config = AuditMaster.objects.filter(
            warehouse=self.warehouse,
            reference_type=self.reference_type,
            reference_number=self.reference_number
        )
        if existing_config.exists():
            audit_config = existing_config[0]
            audit_config.audit_value = self.audit_value
            audit_config.status = self.status
            audit_config.updated_by_id = self.user.id
            audit_config.updation_date = timezone.now()
            audit_config.save()
        else:
            self.errors.append('Configuration does not exist for this reference')

    def post(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = json.loads(self.request.body)
        self.get_request_data()
        self.validate_request_data()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)
        
        self.insert_audit_configuration()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)
        return JsonResponse({'message': 'Audit configuration created successfully'}, status=200)
    
    def put(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = json.loads(self.request.body)
        self.get_request_data()
        self.validate_request_data()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)
        
        self.update_audit_configuration()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)
        return JsonResponse({'message': 'Audit configuration updated successfully'}, status=200)



def get_audit_master_results(start_index, stop_index, temp_data, search_term, order_term, col_num, request, warehouse, filters):
    """
    Audit Master datatable.
    """

    sort_type = request.GET.get('sort_type', 0)
    sorting_key = request.GET.get('sort_by_column')
    column_filters = request.GET.get('columnFilter', {})
    count = True if request.GET.get('count', '') == 'true' else False


    timezone = request.timezone

    column_filters = prepare_filters(column_filters)
    audit_filters = {'warehouse_id': warehouse.id, 'status' :1, **column_filters}

    order_by = ['-id']
    if sorting_key:
        if sort_type == '1':
            sorting_key = f"-{sorting_key}"
        order_by.insert(0, sorting_key)
    master_data = AuditMaster.objects.filter(**audit_filters).values().order_by(*order_by)
    
    if count:
        temp_data['count'] =  master_data.count()
        return
    for audit_data in master_data[start_index: stop_index]:

        json_data = audit_data.pop('json_data', {}) or {}
        audit_data['json_data__created_by'] = json_data.get('created_by', '')
        audit_data['json_data__updated_by'] = json_data.get('updated_by', '')
        
        audit_data['status'] = int(audit_data['status'])

        audit_data['creation_date'] = get_local_date_known_timezone(timezone, audit_data['creation_date'])
        audit_data['updation_date'] = get_local_date_known_timezone(timezone, audit_data['updation_date'])

        temp_data['aaData'].append(audit_data)

def prepare_filters(column_filters):
    column_filters = json.loads(column_filters)
    if 'creation_date' in column_filters:
        column_filters['creation_date__date'] = column_filters.pop('creation_date')
    if 'updation_date' in column_filters:
        column_filters['updation_date__date'] = column_filters.pop('updation_date')
    if 'status' in column_filters:
        column_filters['status'] = column_filters.pop('status')
    if 'reference_type' in column_filters:
        column_filters['reference_type'] = column_filters.pop('reference_type')
    if 'reference_number' in column_filters:
        column_filters['reference_number__icontains'] = column_filters.pop('reference_number')
    return column_filters
