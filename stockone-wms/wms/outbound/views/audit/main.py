"""
Outbound Audit Module

This module implements the audit functionality for outbound operations.
It provides classes for audit processing, flagging, and resolution.

The main components are:
- AuditSet: Handles audit processing and resolution
- AuditFlagging: Handles flagging LPNs for audit based on configured criteria
"""

import json
import random
import traceback
from collections import defaultdict
from django.contrib.postgres.aggregates import <PERSON><PERSON>yAgg
from copy import deepcopy

from django.db import transaction
from django.db.models import Count, F, Max, Sum, Value, When, Case
from django.http import JsonResponse
from django.utils import timezone

# Core operations imports
from core_operations.views.common.main import WMSListView, get_local_date_known_timezone
from core_operations.views.common.validation import update_order_header_status
from core_operations.views.services.packing_service import PackingService

# Inbound imports
from inbound.models import POLocation, PutawayMapping
from inbound.views.grn.create_grn import create_grn_lpn_transactions
from inbound.views.putaway.confirmation import create_putaway_task_for_multiple

#Core Imports
from core.models import SKUMaster

# Inventory imports
from inventory.models import LocationMaster, StockDetail, BatchDetail

# LMS imports
from lms.models import TaskMaster

# Outbound imports
from outbound.models import (AuditDetail, AuditEntry, AuditMaster, Order,
                            OrderDetail, Picklist, SellerOrderSummary)
from outbound.views.audit.constants import (
    AUDIT_MANDATORY_FIELDS, STATUS_TEXT_MAPPING, VALID_REFERENCE_TYPES
)

# Logging
from wms_base.wms_utils import init_logger

log = init_logger('logs/audit_process.log')

class AuditSet(WMSListView):

    def post(self, *args, **kwargs):
        self.errors = []
        self.set_user_credientials()
        self.validate_request_data()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=400)
        try:
            if self.resolve_flag:
                log.info(f"Audit Resolution started for {self.lpn_number} by {self.request.user.username}")
                self.validate_and_prepare_resolution_data()
            else:
                log.info(f"Audit Process started for {self.lpn_number} by {self.request.user.username}")
                self.validate_and_prepare_audit_data()
            if self.errors:
                return JsonResponse({'errors': self.errors}, status=400)
            self.insert_data()
        except Exception as e:
            log.debug(traceback.format_exc())
            log.error(f"Audit Process failed with error {e}")
            return JsonResponse({'errors': [f"Audit Process failed with error {e}"]}, status=400)
        return JsonResponse({'message': 'Audit Processed Successfully'}, status=200)

    def validate_request_data(self):
        self.request_data = json.loads(self.request.body)
        self.timezone = self.request.timezone
        self.resolve_flag = self.request_data.get('resolve_flag')
        for key in AUDIT_MANDATORY_FIELDS:
            if not self.request_data.get(key):
                self.errors.append(f"{key} is mandatory")
                return

        self.lpn_number = self.request_data.get('lpn_number')
        self.items = self.request_data.get('items')
        for item in self.items:
            sku_code = item.get('sku_code', '')
            if not sku_code:
                self.errors.append("SKU code is required")
            batch_number = item.get('batch_number', '')
            if not batch_number:
                self.errors.append("Batch number is required")

        if self.errors:
            return

        self.audit_update_objects_list, self.sos_update_objects, self.picklist_update_objects, self.order_update_objects, self.pull_to_locate_records, self.stock_update_objects, self.new_sos_objects = [], [], [], [], [], [], []

    def validate_and_prepare_audit_data(self):
        self.audit_object = AuditEntry.objects.filter(warehouse=self.warehouse, audit_reference=self.lpn_number, status=1)
        if not self.audit_object:
            self.errors.append("Audit not found for the given LPN number")
            return
        self.audit_object = self.audit_object[0]
        existing_data = AuditDetail.objects.filter(
            audit_entry__warehouse=self.warehouse,
            audit_entry__audit_reference=self.lpn_number
        )
        audit_objects_data, audit_picked_quantity_dict, update_objects_list = defaultdict(list), defaultdict(int), []
        for item in existing_data:
            unique_key = (item.sku_code, item.batch_number)
            audit_objects_data[unique_key].append(item)
            audit_picked_quantity_dict[unique_key] += item.picked_quantity

        self.audit_success = True
        for item in self.items:
            sku_code = item.get('sku_code', '')
            batch_number = item.get('batch_number', '')
            picked_quantity = item.get('picked_quantity', 0)
            audit_quantity = item.get('audit_quantity', 0)
            if picked_quantity != audit_quantity:
                self.audit_success = False
            reason = item.get('reason')
            if picked_quantity != audit_quantity and not reason:
                self.errors.append("Reason is required for quantity mismatch")
                return

            unique_key = (sku_code, batch_number)
            pick_quantity = audit_picked_quantity_dict.get(unique_key, 0)
            if pick_quantity != picked_quantity:
                self.errors.append(f"Picked quantity for {sku_code} and {batch_number} does not match")
                return
            audit_objects = audit_objects_data.get(unique_key)
            if not audit_objects:
                self.errors.append(f"Data for {sku_code} and {batch_number} does not exist")
                return

            for audit_obj in audit_objects:
                audit_obj.audit_quantity = audit_quantity
                audit_quantity = 0
                audit_obj.reason = reason
                update_objects_list.append(audit_obj)

        self.audit_update_objects_list = update_objects_list

    def validate_and_prepare_resolution_data(self):
        self.audit_object = AuditEntry.objects.filter(warehouse=self.warehouse, audit_reference=self.lpn_number, status=2)
        if not self.audit_object:
            self.errors.append("Audit Resolution not found for the given LPN number")
            return
        self.audit_object = self.audit_object[0]
        self.picklist_number = self.audit_object.reference_number
        existing_data = AuditDetail.objects.filter(
            audit_entry__warehouse=self.warehouse,
            audit_entry__audit_reference=self.lpn_number,
            reason__gt=''
        ).values('stock__receipt_number', 'sku_code', 'batch_number', 'stock__batch_detail_id', 'reason')
        putaway_location_filters = {
            'warehouse_id' : self.warehouse.id,
            'status' : 1,
            'transaction_type' : 'outbound_qc',
            'process_type' : 'cancelled_picklist',
        }
        putaway_zones = dict(PutawayMapping.objects.filter(**putaway_location_filters).values_list('reason','zone_id'))
        putaway_locations_data_list = list(LocationMaster.objects.filter(status=1, filled_capacity__lt=F('max_capacity'), zone_id__in=putaway_zones.values()).values('zone_id','id').order_by('fill_sequence'))
        putaway_zones_data = {}
        for putaway_location in putaway_locations_data_list:
            if putaway_location['zone_id'] not in putaway_zones_data:
                putaway_zones_data[putaway_location['zone_id']] = putaway_location['id']

        audit_objects_data, reason_location_mapping, batch_id_mapping = defaultdict(list), {}, {}
        for item in existing_data:
            zone_id = putaway_zones.get(item['reason'], '')
            location_id = putaway_zones_data.get(zone_id, 0)
            if not location_id:
                self.errors.append(f"Location not found for reason {item['reason']}")
                return
            unique_key = (item['sku_code'], item['batch_number'])
            reason_location_mapping[item['reason']] = location_id
            batch_id_mapping[(item['sku_code'], item['batch_number'])] = item['stock__batch_detail_id']
            audit_objects_data[unique_key].append(item['stock__receipt_number'])

        self.cancel_picklist_data, self.sos_ids = {}, []
        for item in self.items:
            sku_code = item.get('sku_code', '')
            batch_number = item.get('batch_number', '')
            picked_quantity = item.get('picked_quantity', 0)
            audit_quantity = item.get('audit_quantity', 0)
            reason = item.get('reason')
            location_id = reason_location_mapping.get(reason, 0)
            if not location_id:
                self.errors.append(f"Location not found for reason {reason}")
                return
            batch_detail_id = batch_id_mapping.get((sku_code, batch_number), 0)
            if not batch_detail_id:
                self.errors.append(f"Data not found for sku code {sku_code} and batch number {batch_number}")
                return
            if picked_quantity > audit_quantity:
                sos_ids = audit_objects_data.get((sku_code, batch_number), [])
                self.cancel_picklist_data[(sku_code, batch_number)] = {
                    'cancelled_quantity' : picked_quantity - audit_quantity,
                    'sos_ids' : sos_ids,
                    'location_id' : location_id,
                    'batch_detail_id' : batch_detail_id
                }
                self.sos_ids.extend(sos_ids)

        if self.cancel_picklist_data:
            self.cancel_picklist_process()

    def cancel_picklist_process(self):
        sos_data = SellerOrderSummary.objects.select_related('picklist__order').in_bulk(self.sos_ids)
        stock_data = StockDetail.objects.select_related('batch_detail__sku').filter(receipt_number__in=self.sos_ids, receipt_type='so_picking', quantity__gt=0, sku__user=self.warehouse.id, lpn_number=self.lpn_number)
        self.stock_data_dict = {stock.receipt_number: stock for stock in stock_data}
        for data in self.cancel_picklist_data.values():
            cancelled_quantity = data['cancelled_quantity']
            if not cancelled_quantity:
                continue
            location_id = data['location_id']
            batch_detail_id = data['batch_detail_id']
            for sos_id in data['sos_ids']:
                sos_obj = sos_data.get(sos_id)
                if not sos_obj:
                    log.info(f"Sos obj not found for sos id {sos_id} and lpn_number {self.lpn_number}")
                    continue
                stock_obj = self.stock_data_dict.get(sos_id)
                if not stock_obj:
                    log.info(f"Stock not found for sos id {sos_id} and lpn_number {self.lpn_number}")
                    continue
                picklist_record = sos_obj.picklist
                order_record = picklist_record.order
                cancel_quantity = min(cancelled_quantity, picklist_record.picked_quantity)
                cancelled_quantity -= cancel_quantity
                if cancel_quantity == sos_obj.quantity:
                    sos_obj.order_status_flag='cancelled'
                else:
                    sos_obj.quantity -= cancel_quantity
                    
                    new_sos_obj = deepcopy(sos_obj.__dict__)
                    del new_sos_obj['id']
                    del new_sos_obj['_state']
                    new_json_data = {
                        'cancelled_by_user': self.request.user.username,
                        'cancelled_reason': 'Audit',
                        'old_sos_id': sos_obj.id
                    }
                    new_sos_obj['json_data'] = new_json_data
                    new_sos_obj['quantity'] = cancel_quantity
                    new_sos_obj['order_status_flag'] = 'cancelled'
                    self.new_sos_objects.append(SellerOrderSummary(**new_sos_obj))
                stock_obj.quantity -= cancel_quantity
                self.stock_update_objects.append(stock_obj)
                self.sos_update_objects.append(sos_obj)
                picklist_record.cancelled_quantity += cancel_quantity
                if picklist_record.picked_quantity == picklist_record.cancelled_quantity:
                    picklist_record.status='cancelled'
                self.picklist_update_objects.append(picklist_record)
                order_record.quantity += cancel_quantity
                order_record.status = 1
                self.order_update_objects.append(order_record)
                po_location_dict = {
                    'picklist_id' : picklist_record.id,
                    'quantity': cancel_quantity,
                    'original_quantity':cancel_quantity,
                    'status': 1,
                    'location_id': location_id,
                    'batch_detail_id': batch_detail_id,
                    'sku_id':picklist_record.sku_id,
                    'putaway_type':'cancelled_picklist',
                    'account_id': self.warehouse.userprofile.id,
                    'json_data': {'cancelled_type': 'Audit Cancelled Picklist', 'request_user': self.request.user.username}
                }
                self.pull_to_locate_records.append(POLocation(**po_location_dict))
        
        self.packing_data = {
            "user": self.user.username,
            "warehouse": self.warehouse.username,
            "transaction_number": str(self.picklist_number),
            "transaction_type": "so_packing",
            "packing_details": [
                {
                    "lpn_number": self.lpn_number,
                    "items": [],
                }
            ],
            "status": "closed"
        }


    def insert_data(self):
        packing_items = []
        with transaction.atomic('default'):
            if self.audit_update_objects_list:
                AuditDetail.objects.bulk_update_with_rounding(self.audit_update_objects_list, ['audit_quantity', 'reason'])

            if self.resolve_flag:
                self.audit_object.resolved_by_id = self.user.id
                self.audit_object.status = 3
            else:
                self.audit_object.status = 3 if self.audit_success else 2
                self.audit_object.auditor_id = self.user.id
                self.audit_object.audit_date = timezone.now()
            self.audit_object.save()

            if self.order_update_objects:
                OrderDetail.objects.bulk_update_with_rounding(self.order_update_objects, ['quantity', 'status'])
                update_order_header_status(self.warehouse, self.order_update_objects)

            if self.picklist_update_objects:
                Picklist.objects.bulk_update_with_rounding(self.picklist_update_objects, ['cancelled_quantity', 'status'])

            if self.sos_update_objects:
                SellerOrderSummary.objects.bulk_update_with_rounding(self.sos_update_objects, ['quantity', 'order_status_flag'])
                for sos_obj in self.sos_update_objects:
                    stock_obj = self.stock_data_dict.get(sos_obj.id)
                    if not stock_obj:
                        continue
                    batch_no = stock_obj.batch_detail.batch_no
                    manufactured_date = stock_obj.batch_detail.manufactured_date or ""
                    expiry_date = stock_obj.batch_detail.expiry_date or ""
                    
                    pack_item = {
                        "transaction_id": sos_obj.id,
                        "sku_code": stock_obj.batch_detail.sku.sku_code,
                        "sku_description": stock_obj.batch_detail.sku.sku_desc,
                        "mrp": sos_obj.picklist.order.mrp,
                        "unit_price": sos_obj.picklist.order.unit_price,
                        "batch_details": {
                            "batch_number" : batch_no,
                            "batch_reference" : stock_obj.batch_detail.batch_reference,
                            "manufactured_date" : get_local_date_known_timezone(self.timezone, manufactured_date, send_date=True).strftime('%Y-%m-%d') if manufactured_date else '',
                            "expiry_date" : get_local_date_known_timezone(self.timezone, expiry_date, send_date=True).strftime('%Y-%m-%d') if expiry_date else ''
                        },
                        "json_data": {
                            "pack_uom_quantity": 1,
                            "packed_lpn" : True
                        },
                        "packed_quantity": stock_obj.quantity
                    }
                    packing_items.append(pack_item)
            
            if self.new_sos_objects:
                sos_objs = SellerOrderSummary.objects.bulk_create_with_rounding(self.new_sos_objects)
                new_stock_objs = []
                for sos_obj in sos_objs:
                    old_sos_id = sos_obj.json_data.get('old_sos_id')
                    stock_obj = self.stock_data_dict.get(old_sos_id)
                    if not stock_obj:
                        log.info(f"Stock not found for sos id {old_sos_id} and lpn_number {self.lpn_number}")
                        continue
                    if stock_obj.quantity ==0:
                        stock_obj.receipt_number = sos_obj.id
                        continue
                    new_stock_obj = deepcopy(stock_obj.__dict__)
                    del new_stock_obj['id']
                    del new_stock_obj['_state']
                    new_stock_obj['receipt_number'] = sos_obj.id
                    new_stock_obj['quantity'] = 0
                    new_stock_objs.append(StockDetail(**new_stock_obj))
                
                if new_stock_objs:
                    StockDetail.objects.bulk_create_with_rounding(new_stock_objs)
            
            if self.stock_update_objects:
                StockDetail.objects.bulk_update_with_rounding(self.stock_update_objects, ['quantity', 'receipt_number'])
            

            if self.pull_to_locate_records:
                po_loc_objs = POLocation.objects.bulk_create_with_rounding(self.pull_to_locate_records, batch_size=500)
                create_putaway_task_for_multiple(po_loc_objs)
                pol_ids = [po_loc_obj.id for po_loc_obj in po_loc_objs]
                extra_params = {'request_headers': self.request.headers, 'request_user': self.request.user, 'request_meta': self.request.META, 'headers': self.request.headers}
                create_grn_lpn_transactions(self.request.user.id, self.warehouse.id, extra_params = extra_params, pol_ids=pol_ids)
            
            if packing_items:
                self.packing_data['packing_details'][0]['items'] = packing_items
                request_dict = {
                    "request_headers": {
                        "Warehouse": self.request.headers.get("Warehouse"),
                        "Authorization": self.request.headers.get('Authorization', '')
                    },
                    "request_meta": {'HTTP_HOST': self.request.META.get('HTTP_HOST', '')},
                    "request_scheme": self.request.scheme,
                }
                packing_service_instance = PackingService(request_dict, self.user, self.warehouse)
                packing_details, packing_service_errors = packing_service_instance.create_packing(self.packing_data)
                log.info(f"Create packing response packing details {packing_details} and errors {packing_service_errors}")
    
    def put(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = json.loads(self.request.body)
        self.timezone = self.request.timezone
        self.lpn_numbers = self.request_data.get('lpn_numbers', [])
        if not self.lpn_numbers:
            return JsonResponse({'errors': ["LPN numbers are mandatory"]}, status=400)
        self.cancel_audit_entry()
        if self.errors:
            return JsonResponse({'errors': self.errors}, status=self.status)
        return JsonResponse({'message': 'Audit Cancelled Successfully'}, status=self.status)
    
    def cancel_audit_entry(self):
        self.errors = []
        audit_objects = AuditEntry.objects.filter(warehouse=self.warehouse, audit_reference__in=self.lpn_numbers, status=1)
        found_lpns, update_objects = set(), []
        for audit_entry in audit_objects:
            audit_entry.status = 3
            json_data = audit_entry.json_data or {}
            json_data.update({'cancelled_by': self.request.user.username})
            audit_entry.json_data = json_data
            found_lpns.add(audit_entry.audit_reference)
            update_objects.append(audit_entry)
        
        self.status = 400
        if found_lpns:
            AuditEntry.objects.bulk_update_with_rounding(update_objects, ['status', 'json_data'])
            self.status = 200
        
        if set(self.lpn_numbers) - found_lpns:
            self.errors.append("No Audit found for the LPN numbers - %s" % ",".join(set(self.lpn_numbers) - found_lpns))
            if self.status == 200:
                self.status = 207
    
    def get(self, *args, **kwargs):
        self.set_user_credientials()
        self.request_data = self.request.GET
        self.timezone = self.request.timezone
        self.start_index, self.end_index = self.get_pagination_info()
        audit_data = self.get_pending_audit_data()
        return JsonResponse({"message" : "Success", 'data': audit_data}, status=200)

    def get_pending_audit_data(self):
        header_level = self.request_data.get('header_level', 'false') == 'true'
        self.is_datatable = self.request_data.get('is_datatable')
        self.resolve_flag = self.request_data.get('resolve_flag', 'false') == 'true'
        if header_level or self.is_datatable:
            return self.get_pending_picklist_audit_data()
        else:
            return self.get_pending_lpn_audit_data()

    def get_pending_picklist_audit_data(self):
        audit_filters, stock_filters, order_filters = self.prepare_audit_filters()
        audit_entries = AuditEntry.objects.filter(**audit_filters).values('reference_number', 'status', 'audit_reference', 'auditor__username', 'audit_date').order_by(self.sorting_key)[self.start_index:self.end_index]
        picklist_numbers, lpn_numbers = set(), set()
        for audit_entry in audit_entries:
            picklist_numbers.add(audit_entry['reference_number'])
            lpn_numbers.add(audit_entry['audit_reference'])
        if not stock_filters.get('lpn_number'):
            stock_filters['lpn_number__in'] = lpn_numbers
        if not stock_filters.get('transact_number'):
            stock_filters['transact_number__in'] = picklist_numbers

        stock_data = list(StockDetail.objects.filter(**stock_filters).values('lpn_number', 'transact_number', 'grn_number').annotate(skus_count=Count('sku_id', distinct=True), total_quantity=Sum('quantity')))
        stock_data_dict, item_data_dict, order_references = defaultdict(set), {}, set()
        for stock in stock_data:
            unique_key = (str(stock['transact_number']), stock['lpn_number'])
            stock_data_dict[unique_key].add(stock['grn_number'])
            order_references.add(stock['grn_number'])
            if unique_key not in item_data_dict:
                item_data_dict[unique_key] = {
                    'skus_count' : 0,
                    'total_quantity' : 0
                }
            item_data_dict[unique_key]['skus_count'] += stock['skus_count']
            item_data_dict[unique_key]['total_quantity'] += stock['total_quantity']
        if not order_filters.get('order_reference'):
            order_filters['order_reference__in'] = order_references

        order_data = dict(Order.objects.filter(**order_filters).values_list('order_reference', 'customer__customer_reference'))
        order_data_dict = defaultdict(set)
        for unique_key, order_references in stock_data_dict.items():
            for order_reference in order_references:
                customer_reference = order_data.get(order_reference, '')
                if customer_reference:
                    order_data_dict[unique_key].add(customer_reference)
        final_data_list = []
        for audit_entry in audit_entries:
            unique_key = (audit_entry['reference_number'], audit_entry['audit_reference'])
            customer_references = order_data_dict.get(unique_key, '')
            audit_date = get_local_date_known_timezone(self.timezone, audit_entry['audit_date'], send_date=True).strftime('%Y-%m-%d') if audit_entry['audit_date'] else ''
            if not customer_references:
                continue
            for customer_ref in customer_references:
                status = STATUS_TEXT_MAPPING.get(audit_entry['status'], '')

                final_data = {
                    'picklist_number': audit_entry['reference_number'],
                    'lpn_number' : audit_entry['audit_reference'],
                    'customer_reference': customer_ref,
                    'status': status,
                    'auditor': audit_entry['auditor__username'],
                    'audit_date': audit_date,
                    'skus_count' : 0,
                    'total_quantity' : 0
                }
                final_data.update(item_data_dict.get(unique_key, {}))
                final_data_list.append(final_data)

        return final_data_list

    def get_pending_lpn_audit_data(self):
        self.lpn_number = self.request_data.get('lpn_number', '')
        if not self.lpn_number:
            return []
        filters = {
            'audit_entry__warehouse_id' : self.warehouse.id,
            'audit_entry__status' : 2 if self.resolve_flag else 1,
            'audit_entry__audit_reference' : self.lpn_number
        }
        if self.resolve_flag:
            filters['reason__gt'] = ''
        values_list = [ 'sku_code', 'batch_number' ]
        audit_data = list(AuditDetail.objects.filter(**filters).values(*values_list).annotate(
            picked_quantity=Sum('picked_quantity'), audit_quantity=Sum('audit_quantity'), reason=Max('reason'))
        )
        batches = set([audit['batch_number'] for audit in audit_data])
        skus = set([audit['sku_code'] for audit in audit_data])
        batch_data_list = list(BatchDetail.objects.filter(warehouse_id=self.warehouse.id, batch_no__in=batches).values('batch_no', 'expiry_date', 'mrp').annotate(batch_keys=Case(When(batch_identifier__isnull=False,then=ArrayAgg('batch_identifier__batch_identifier')), default=Value([]))))
        sku_data_list = list(SKUMaster.objects.filter(user=self.warehouse.id, sku_code__in=skus).values('sku_code', 'sku_desc', sku_image=F('image_url')).annotate(ean_numbers=Case(When(eannumbers__isnull=False,then=ArrayAgg('eannumbers__ean_number')), default=Value([]))))
        batch_data_dict = {}
        sku_data_dict = {}
        for batch_data in batch_data_list:
            expiry_date = get_local_date_known_timezone(self.timezone, batch_data['expiry_date'], send_date=True).strftime('%Y-%m-%d') if batch_data['expiry_date'] else ''
            batch_data['expiry_date'] = expiry_date
            batch_data_dict[batch_data['batch_no']] = batch_data
        for sku_data in sku_data_list:
            sku_data_dict[sku_data['sku_code']] = sku_data
        for audit in audit_data:
            audit.update(batch_data_dict.get(audit['batch_number'], {}))
            audit.update(sku_data_dict.get(audit['sku_code'], {}))
        return audit_data

    def prepare_audit_filters(self):
        audit_filters = {
            'warehouse_id' : self.warehouse.id,
        }
        if self.is_datatable:
            audit_filters['status__in'] = [1,2]
        elif self.resolve_flag:
            audit_filters['status'] = 2
        else:
            audit_filters['status'] = 1
        stock_filters = {
            'sku__user' : self.warehouse.id,
            'quantity__gt' : 0,
            'receipt_type' : 'so_picking'
        }
        order_filters = {
            'warehouse_id' : self.warehouse.id
        }
        self.sorting_key = self.request_data.get('sorting_key', '-id') or '-id'
        self.sorting_key = self.sorting_key.replace('picklist_number', 'reference_number')
        self.sorting_key = self.sorting_key.replace('lpn_number', 'audit_reference')
        picklist_number = self.request_data.get('picklist_number', '')
        auditor = self.request_data.get('auditor', '')
        lpn_number = self.request_data.get('lpn_number', '')
        from_date = self.request_data.get('from_date', '')
        to_date = self.request_data.get('to_date', '')
        order_reference = self.request_data.get('order_reference', '')
        customer_reference = self.request_data.get('customer_reference', '')
        if picklist_number:
            audit_filters['reference_number'] = picklist_number
            stock_filters['transact_number'] = picklist_number
        if auditor:
            audit_filters['auditor__username'] = auditor
        if lpn_number:
            audit_filters['audit_reference'] = lpn_number
            stock_filters['lpn_number'] = lpn_number
        if from_date:
            audit_filters['audit_date__gte'] = from_date
        if to_date:
            audit_filters['audit_date__lte'] = to_date
        if order_reference:
            stock_filters['grn_number'] = order_reference
            order_filters['order_reference'] = order_reference
        if customer_reference:
            order_filters['customer__customer_reference'] = customer_reference
        return audit_filters, stock_filters, order_filters

    def get_pagination_info(self):
        start_index = int(self.request_data.get('offset', 0))
        limit = int(self.request_data.get('limit', 10))
        end_index = start_index + limit
        return start_index, end_index

class AuditFlagging():
    def __init__(self, warehouse, user, stock_detail_ids, timezone):
        self.warehouse = warehouse
        self.user = user
        self.stock_detail_ids = stock_detail_ids
        self.timezone = timezone

    def audit_flagging_process(self):
        try:
            log.info(f"Audit Flagging started for stock - {self.stock_detail_ids} by {self.user.username}")
            self.fetch_config_data()
            if not self.config_data:
                return
            self.fetch_reference_data()
            self.check_lpns_for_audit()
            self.create_audit_entries()
        except Exception as e:
            log.debug(traceback.format_exc())
            log.error(f"Audit Flagging failed with error {e}")

    def fetch_config_data(self):
        self.config_data = { reference_type : {} for reference_type in VALID_REFERENCE_TYPES}
        audit_configs = list(AuditMaster.objects.filter(status=1, reference_type__in=VALID_REFERENCE_TYPES, warehouse_id=self.warehouse.id).values().order_by('-audit_type'))
        for audit_config in audit_configs:
            self.config_data[audit_config['reference_type']][audit_config['reference_number']] = audit_config

    def fetch_reference_data(self):
        self.audit_lpn_data = { reference_type : defaultdict(set) for reference_type in self.config_data.keys() }
        self.lpn_order_mapping, self.lpn_sos_mapping, self.lpn_picklist_mapping, self.order_references, self.sos_ids = defaultdict(set), defaultdict(set), {}, set(), set()
        stock_values = [
            'id', 'lpn_number', 'grn_number', 'receipt_number', 'transact_number', 'quantity', 'sku__sku_code',
            'batch_detail__batch_no'
        ]
        self.stock_data = list(StockDetail.objects.filter(id__in=self.stock_detail_ids).values(*stock_values))
        for stock in self.stock_data:
            lpn_number = stock['lpn_number']
            order_reference = stock['grn_number']
            sos_id = stock['receipt_number']
            self.lpn_order_mapping[lpn_number].add(order_reference)
            self.lpn_sos_mapping[lpn_number].add(sos_id)
            self.lpn_picklist_mapping[lpn_number] = str(stock['transact_number'])
            self.order_references.add(order_reference)
            self.sos_ids.add(sos_id)
        
        log.info("LPN Numbers for audit - %s" % list(self.lpn_order_mapping.keys()))

        if self.config_data['customer_reference'] or self.config_data['order_type']:
            self.fetch_order_data()
        if self.config_data['zone'] or self.config_data['sub_zone'] or self.config_data['picker_name']:
            self.fetch_pick_data()


    def fetch_order_data(self):
        order_data = list(Order.objects.filter(warehouse=self.warehouse, order_reference__in=self.order_references).values('order_reference', 'customer__customer_reference', 'order_type'))
        order_data_dict = defaultdict(dict)
        for order in order_data:
            order_reference = order['order_reference']
            customer_reference = order['customer__customer_reference']
            order_type = order['order_type']
            order_data_dict[order_reference]['customer_reference'] = customer_reference
            order_data_dict[order_reference]['order_type'] = order_type

        for lpn_number, order_references in self.lpn_order_mapping.items():
            for order_reference in order_references:
                customer_reference = order_data_dict[order_reference].get('customer_reference', '')
                if customer_reference:
                    if 'all' in self.config_data['customer_reference']:
                        self.audit_lpn_data['customer_reference']['all'].add(lpn_number)
                    elif customer_reference in self.config_data.get('customer_reference', {}):
                        self.audit_lpn_data['customer_reference'][customer_reference].add(lpn_number)
                order_type = order_data_dict[order_reference].get('order_type', '')
                if order_type:
                    if order_type in self.config_data.get('order_type', {}):
                        self.audit_lpn_data['order_type'][order_type].add(lpn_number)
                    elif 'all' in self.config_data.get('order_type'):
                        self.audit_lpn_data['order_type']['all'].add(lpn_number)


    def fetch_pick_data(self):
        picking_data = list(SellerOrderSummary.objects.filter(id__in=self.sos_ids, picklist__user_id=self.warehouse.id).values('id', 'picklist__picklist_number', 'picklist_id', 'picklist__location__zone__zone', 'picklist__location__sub_zone__zone'))
        pick_ids = set(pick['picklist_id'] for pick in picking_data)
        picker_data = dict(TaskMaster.objects.filter(status=True, task_ref_id__in=pick_ids).values_list('task_ref_id', 'employee__user__username'))
        sos_data_dict = defaultdict(dict)
        for pick_data in picking_data:
            picklist_id = pick_data['picklist_id']
            zone = pick_data['picklist__location__zone__zone']
            sub_zone = pick_data['picklist__location__sub_zone__zone']
            picker_name = picker_data.get(picklist_id, '')
            sos_data_dict[pick_data['id']].setdefault('zone', set())
            sos_data_dict[pick_data['id']].setdefault('sub_zone', set())
            sos_data_dict[pick_data['id']].setdefault('picker_name', set())
            if zone in self.config_data['zone'] or 'all' in self.config_data['zone']:
                sos_data_dict[pick_data['id']]['zone'].add(zone)
            if sub_zone in self.config_data['sub_zone'] or 'all' in self.config_data['sub_zone']:
                sos_data_dict[pick_data['id']]['sub_zone'].add(sub_zone)
            if picker_name in self.config_data['picker_name'] or 'all' in self.config_data['picker_name']:
                sos_data_dict[pick_data['id']]['picker_name'].add(picker_name)

        for lpn_number, sos_ids in self.lpn_sos_mapping.items():
            for sos_id in sos_ids:
                sos_data = sos_data_dict[sos_id]
                if not sos_data:
                    continue
                for reference_type in ['zone', 'sub_zone', 'picker_name']:
                    references = sos_data.get(reference_type, set())
                    config_values = self.config_data.get(reference_type, {})
                    if 'all' in config_values:
                        self.audit_lpn_data[reference_type]['all'].add(lpn_number)
                        continue
                    for reference in references:
                        if reference in config_values:
                            self.audit_lpn_data[reference_type][reference].add(lpn_number)

    def check_lpns_for_audit(self):
        self.flagged_lpns, self.audit_objects_list = set(), []
        for reference_type, reference_data in self.config_data.items():
            for reference_number, config_data in reference_data.items():
                lpn_numbers = self.audit_lpn_data[reference_type][reference_number]
                log.info("Audit validation for reference_type - %s, reference_number - %s and lpn_numbers - %s" % (reference_type, reference_number, lpn_numbers))
                for lpn_number in lpn_numbers:
                    if lpn_number in self.flagged_lpns:
                        continue
                    is_valid = self.check_lpn_valid_for_audit(config_data)
                    if is_valid:
                        picklist_number = self.lpn_picklist_mapping[lpn_number]
                        audit_entry = AuditEntry(
                                        warehouse=self.warehouse,
                                        audit_reference=lpn_number,
                                        reference_number=picklist_number,
                                        status=1,
                                        account_id=self.warehouse.userprofile.id
                                    )
                        self.audit_objects_list.append(audit_entry)
                        self.flagged_lpns.add(lpn_number)


    def check_lpn_valid_for_audit(self, config_data):
        audit_type = config_data.get('audit_type')
        audit_value = config_data.get('audit_value')
        flag = False
        with transaction.atomic():
            # Lock the row to prevent concurrent modifications
            audit_config = AuditMaster.objects.select_for_update().get(id=config_data['id'])
            if audit_type == 'fixed_count':
                audit_config.total_count += 1
                if (audit_config.total_count) % audit_value == 0:
                    audit_config.current_counter += 1
                    flag = True
            elif audit_type == 'sampling_percentage':
                audit_config.total_count += 1

                if ((audit_config.current_counter + 1) / audit_config.total_count) * 100 <= audit_value:
                    adjusted_prob = (audit_config.audit_value / 100) * (audit_config.total_count / (audit_config.current_counter + 1))
                    final_prob = min(adjusted_prob, 1.0)
                    if random.random() < final_prob:
                        audit_config.current_counter += 1
                        flag = True

            audit_config.save()

        return flag

    def create_audit_entries(self):
        if self.audit_objects_list:
            audit_entries = AuditEntry.objects.bulk_create_with_rounding(self.audit_objects_list)
            lpn_wise_audit_data = {}
            for audit_entry in audit_entries:
                lpn_wise_audit_data[audit_entry.audit_reference] = audit_entry.id

            audit_detail_objects_list = []
            for stock in self.stock_data:
                audit_entry_id = lpn_wise_audit_data.get(stock['lpn_number'])
                if not audit_entry_id:
                    continue
                audit_detail_objects_list.append(AuditDetail(
                    audit_entry_id=audit_entry_id,
                    sku_code=stock['sku__sku_code'],
                    batch_number=stock['batch_detail__batch_no'],
                    picked_quantity=stock['quantity'],
                    audit_quantity=stock['quantity'],
                    stock_id=stock['id'],
                    account_id = self.warehouse.userprofile.id
                ))
            if audit_detail_objects_list:
                AuditDetail.objects.bulk_create_with_rounding(audit_detail_objects_list)


def get_pending_audit_results(start_index, stop_index, temp_data, search_term, order_term, col_num, request, warehouse, filters):
    """
    Audit Master datatable.
    """

    sort_type = request.GET.get('sort_type', 0)
    sorting_key = request.GET.get('sort_by_column')
    column_filters = request.GET.get('columnFilter', {})
    count = True if request.GET.get('count', '') == 'true' else False
    if sort_type == '1':
        sorting_key = f"-{sorting_key}"

    timezone = request.timezone

    request_data = json.loads(column_filters) or {}
    picklist_number = request_data.pop('reference_number', '')
    lpn_number = request_data.pop('audit_reference', '')
    if picklist_number:
        request_data['picklist_number'] = picklist_number
    if lpn_number:
        request_data['lpn_number'] = lpn_number
    request_data['is_datatable'] = True

    request_data['sorting_key'] = sorting_key

    audit_data_set = AuditSet()
    audit_data_set.warehouse = warehouse
    audit_data_set.timezone = timezone
    audit_data_set.start_index = start_index
    audit_data_set.end_index = stop_index
    audit_data_set.request_data = request_data
    temp_data['aaData'] = audit_data_set.get_pending_audit_data()

