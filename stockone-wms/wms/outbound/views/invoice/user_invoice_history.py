
from collections import defaultdict

from django.http import JsonResponse
from django.db.models import Q

from outbound.models import InvoiceItems, InvoiceDetail
from core_operations.views.common.main import WMSListView
from outbound.views.sales_return.helpers import get_date_time

class InvoiceHistory(WMSListView):

 
    def get(self, *args, **kwargs):
        """
        This method handles the GET request for retrieving invoice history for a user.
        """
        self.set_user_credientials()

        is_valid, data = InvoiceUserHistoryMixin(self.request).get_history()    

        if not is_valid:
            return JsonResponse({'errors': data}, status=400)
        return JsonResponse({"data": data}, status=200)


class InvoiceUserHistoryMixin:

    def __init__(self, request):
        self.user = request.user
        self.warehouse = request.warehouse
        self.request_data = request.GET
        self.timezone = self.warehouse.userprofile.timezone

        self.final_data, self.errors = {}, []

    def get_pagination_details(self):
        """
        Retrieves the pagination details from the request data.
        """
        try:
            self.page_size = int(self.request_data.get('length', 10))
        except Exception:
            self.page_size = 10
        try:
            self.page = int(self.request_data.get('page', 1))
        except Exception:
            self.page = 1

        self.start_index = (self.page - 1) * self.page_size
        self.end_index = self.page * self.page_size
    

    def prepare_filter_params(self):
        """
        prepares filters based on request data
        """


        filters = {
            'warehouse_id': self.warehouse.id
        }
        q_filters = Q()
        request_user = self.request_data.get('user', '')
        order_reference = self.request_data.get('order_reference', '')
        invoice_reference = self.request_data.get('invoice_reference', '')
        
        if order_reference:
            filters['order_reference'] = order_reference
        if invoice_reference:
            q_filters |= Q(invoice__invoice_reference=invoice_reference)
            q_filters |= Q(invoice__challan_number=invoice_reference)

        if invoice_reference or order_reference:
            return filters, q_filters

        invoice_objs = list(InvoiceDetail.objects.filter(invoice_done_by=request_user, warehouse_id=self.warehouse.id)\
                    .order_by('-invoice_date')[self.start_index: self.end_index].values('invoice_reference', 'challan_number'))
        invoices = []
        challans = []
        for invoice_obj in invoice_objs:
            invoice_reference = invoice_obj.get('invoice_reference', '')
            challan_number = invoice_obj.get('challan_number', '')
            if invoice_reference:
                invoices.append(invoice_reference)
            elif challan_number:
                challans.append(challan_number)
        
        filters = {
            'warehouse_id': self.warehouse.id
        }
        if invoices:
            filters['invoice__invoice_reference__in'] = invoices
        if challans:
            filters['invoice__challan_number__in'] = challans
        return filters, q_filters


    def get_history(self):
        """
        retrives all invoices created by a user by pagination
        """
        self.errors = []
        request_user = self.request_data.get('user', '')
        if not request_user:
            self.errors.append("user is mandatory to get invoice history")
            return False, self.errors
        self.get_pagination_details()
        filters, q_filters = self.prepare_filter_params()
        self.invoice_items = list(InvoiceItems.objects.filter(q_filters, **filters).order_by('-invoice__invoice_date').values('invoice__invoice_reference', 'invoice__challan_number', 'invoice__invoice_done_by', 
                                                                           'invoice__total_invoice_value', 'invoice__invoice_date', 'picklist_number', 'order_reference', 
                                                                           'order_date', 'order_type', 'sku_code', 'sku_desc','order_quantity', 'invoice_quantity', 
                                                                           'cancelled_quantity', 'mrp', 'unit_price', 'batch_number', 'manufactured_date', 'expiry_date'))
        final_data = self.prepare_final_data()
        return True, final_data

    def prepare_final_data(self):
        """
        formats the invoice items into invoice wise.
        """

        invoice_ref_sku_map = defaultdict(set)
        for invoice_item in self.invoice_items:
            invoice_reference = invoice_item.get('invoice__invoice_reference', '')
            challan_number = invoice_item.get('invoice__challan_number', '')
            invoice_key = (invoice_reference, challan_number)
            if not self.final_data.get(invoice_key):
                self.final_data[invoice_key] = {
                    'invoice_reference': invoice_reference,
                    'challan_number': challan_number,
                    'invoice_done_by': invoice_item.get('invoice__invoice_done_by', ''),
                    'total_invoice_value': invoice_item.get('invoice__total_invoice_value', 0),
                    'invoice_date': get_date_time(self.timezone, invoice_item.get('invoice__invoice_date', '')),
                    'total_quantity': 0,
                    'total_skus': [],
                    'skus_count': 0,
                    'items': []
                }
            self.final_data[invoice_key]['items'].append({
                'picklist_number': invoice_item.get('picklist_number', ''),
                'order_reference': invoice_item.get('order_reference', ''),
                'order_date': get_date_time(self.timezone, invoice_item.get('order_date', '')),
                'order_type': invoice_item.get('order_type', ''),
                'sku_code': invoice_item.get('sku_code', ''),
                'sku_desc': invoice_item.get('sku_desc', ''),
                'order_quantity': invoice_item.get('order_quantity', 0),
                'invoice_quantity': invoice_item.get('invoice_quantity', 0),
                'cancelled_quantity': invoice_item.get('cancelled_quantity', 0),
                'mrp': invoice_item.get('mrp', 0),
                'unit_price': invoice_item.get('unit_price', 0),
                'batch_number': invoice_item.get('batch_number', ''),
                'manufactured_date': get_date_time(self.timezone, invoice_item.get('manufactured_date', '')),
                'expiry_date': get_date_time(self.timezone, invoice_item.get('expiry_date', ''))
            })
            self.final_data[invoice_key]['total_quantity'] += invoice_item.get('invoice_quantity', 0)
            invoice_ref_sku_map[invoice_key].add(invoice_item.get('sku_code', ''))
        

        for invoice_key, data in self.final_data.items():
            data['total_skus'] = list(invoice_ref_sku_map.get(invoice_key, []))
            data['skus_count'] = len(data['total_skus'])
        
        return list(self.final_data.values())
