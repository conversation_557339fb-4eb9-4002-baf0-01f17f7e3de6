#django imports
from django.db import models

#wms imports
from wms_base.wms_utils import (
    TenantBaseModel, ForeignKey
)
from wms_base.models import User


class AuditMaster(TenantBaseModel):
    """
    Model for storing audit configuration settings.
    
    This table stores the configuration for different audit criteria such as
    customer, zone, picker, order type, etc. Each configuration can specify
    either a fixed count or percentage-based sampling approach.
    """
    warehouse = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    reference_type = models.CharField(max_length=64, default='') # Reference type can be: CUSTOMER, ZONE, SUB_ZONE, PICKER, ORDER_TYPE
    reference_number = models.CharField(max_length=64, default='') # Reference number is the specific value for the reference type (e.g., customer name, zone name)
    audit_type = models.CharField(max_length=32, choices=(
        ('FIXED_COUNT', 'Fixed Count'),
        ('SAMPLING', 'Sampling Percentage')
    ), default='FIXED_COUNT')
    audit_value = models.FloatField(default=0) # For FIXED_COUNT, this is the Nth item to audit (e.g., every 5th LPN) For SAMPLING, this is the percentage of items to audit (e.g., 30%)
    current_counter = models.IntegerField(default=0) # Current counter value for FIXED_COUNT approach
    total_count = models.IntegerField(default=0) # Total count for SAMPLING approach
    counter_reset_days = models.IntegerField(default=1)
    last_reset_date = models.DateTimeField(auto_now_add=True)
    status = models.BooleanField(default=True)
    json_data = models.JSONField(null=True, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'AUDIT_MASTER'
        unique_together = ('warehouse', 'reference_type', 'reference_number')


class AuditEntry(TenantBaseModel):
    """
    Model for storing audit entries.
    
    This table stores all references that have been flagged for audit,
    along with their current status and audit results.
    """
    warehouse = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    audit_reference = models.CharField(max_length=128, blank=True, null=True) # This is the LPN number or other reference being audited
    reference_number = models.CharField(max_length=128, blank=True, null=True) # This is the picklist number or other reference
    reference_type = models.CharField(max_length=64, default='LPN') # Default is LPN but can be other types in the future
    status = models.PositiveIntegerField(choices=(
        (1, 'Open for Audit'),
        (2, 'Failed Audit, Pending Resolution'),
        (3, 'Completed')
    ), default=1)
    auditor = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True, related_name='auditor')
    audit_date = models.DateTimeField(null=True, blank=True)
    resolved_by = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True, related_name='resolved_by')
    json_data = models.JSONField(null=True, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'AUDIT_ENTRY'
        index_together = ('reference_number', 'reference_type', 'status', 'warehouse')

class AuditDetail(TenantBaseModel):
    """
    Model for storing audit details.
    
    This table stores the detailed results of each audit, including
    the expected and actual quantities for each SKU/batch combination.
    """
    audit_entry = ForeignKey(AuditEntry, on_delete=models.CASCADE, blank=True, null=True)
    sku_code = models.CharField(max_length=128, blank=True, null=True)
    batch_number = models.CharField(max_length=128, blank=True, null=True)
    stock = ForeignKey('inventory.StockDetail', on_delete=models.CASCADE, blank=True, null=True)
    picked_quantity = models.FloatField()
    audit_quantity = models.FloatField()
    reason = models.CharField(max_length=256, blank=True, null=True)
    json_data = models.JSONField(null=True, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'AUDIT_DETAIL'
        index_together = ('audit_entry', 'stock')
