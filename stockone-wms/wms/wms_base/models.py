#package imports
from datetime import date, timedelta
from simple_history.models import HistoricalRecords
from simple_history.utils import bulk_create_with_history, bulk_update_with_history
import json
from wms_base.middleware.uuid_grenerate import request_id_local

#apps imports
from django.apps import apps
from django.core.exceptions import ValidationError

#django imports
from django.db import models, transaction
from django.db.models import ForeignKey, AutoField
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.db.models.signals import pre_save
from django.conf import settings
from django.contrib.auth.models import Group
from django.contrib.auth.models import PermissionsMixin, AbstractUser, Permission
from django.core.files.storage import FileSystemStorage
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.translation import gettext_lazy as _
from django.core.exceptions import ObjectDoesNotExist
from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import receiver
from django.core.exceptions import ValidationError
from django.core.cache import cache

from django_multitenant.mixins import TenantManagerMixin, TenantModelMixin
from django.utils import timezone

from .choices import ROLE_TYPE_CHOICES,CURRENCY_CHOICES
from wms.storage_backends import PublicMediaStorage, PrivateMediaStorage
from wms_base.redis_wrapper import RedisJSONWrapper

from wms_base.middleware.current_user import CurrentUserMiddleware
from wms_base.scripts.script_validators import ScriptValidator


#import model validators
from wms_base.model_validators import validate_ip_list

#cache ops
from cacheops import invalidate_model

#django settings
from django.conf import settings
USE_CACHE_OPS = getattr(settings, 'USE_CACHE_OPS', False)
CACHEOPS = getattr(settings, 'CACHEOPS', {})


# from longerusername import MAX_USERNAME_LENGTH
# Create your models here

PERCENTAGE_VALIDATOR = [MinValueValidator(0), MaxValueValidator(100)]

#Begining Of Base Models Dont Touch This
def current_year():
    return date.today().year

def get_decimal_places(account_id):
    misc_detail_model = apps.get_model('core', 'MiscDetail')
    decimal_places = 0
    if not (hasattr(request_id_local, 'thread_decimal_limit') and hasattr(request_id_local, 'request_id')):
        misc_obj = misc_detail_model.objects.filter(account_id = account_id, misc_type = 'decimal_limit').first()
        if misc_obj:
            decimal_places = misc_obj.misc_value
        if decimal_places and isinstance(decimal_places, str) and decimal_places.isdigit():
            decimal_places = int(decimal_places)
        request_id_local.thread_decimal_limit = decimal_places
    else:
        decimal_places = request_id_local.thread_decimal_limit
    return decimal_places

def pre_save_round_fields(instance, account_id, fields_to_round, decimal_places = None):
    if not decimal_places:
        decimal_places = get_decimal_places(account_id)

    for field_name in fields_to_round:
        # Get the field value
        field_value = getattr(instance, field_name)

        if field_value not in [None, '', 'null'] and decimal_places not in [None, '', 'false']:
            if field_value and isinstance(field_value, str):
                try:
                    field_value = float(field_value)
                except ValueError:
                    raise Exception('Invalid decimal field!')

            # Round the field value
            rounded_value = round(field_value, decimal_places)

            # Update the field value in the instance
            setattr(instance, field_name, rounded_value)
    return instance

def user_has_role_or_permission(user, required_permission):
    # Check direct permissions
    if user.has_perm(required_permission):
        return True

    # Check role permissions
    if user.roles.filter(permissions__codename=required_permission).exists():
        return True

    return False

class User(AbstractUser):
    current_warehouse =  models.ForeignKey("self", on_delete=models.CASCADE, related_name="attached_warehouse", null=True, blank=True)
    roles = models.ManyToManyField("Role", related_name='users', blank=True, null=True)
    email = models.EmailField(unique=True)
    locked = models.BooleanField(default=False)
    failed_attempts = models.IntegerField(default=0)
    password_reset_required = models.BooleanField(default=False)
    email_verified = models.BooleanField(default=False)
    def __str__(self):
        return self.username

    def has_permission(self, perm, obj=None):
        return user_has_role_or_permission(self, perm)

class TenantModelQuerySet(models.QuerySet):
    def update(self, **kwargs):
        updated = super().update(**kwargs)
        self.model.objects.invalidate_cache()
        return updated

    def bulk_update(self, *args, **kwargs):
        updated = super().bulk_update(*args, **kwargs)
        self.model.objects.invalidate_cache()
        return updated


class TenantManager(TenantManagerMixin, models.Manager):

    _queryset_class = TenantModelQuerySet

    def get_queryset(self):
        # Call the parent `get_queryset` which includes TenantManagerMixin logic
        queryset = super().get_queryset()

        # Ensure it returns an instance of the custom QuerySet
        if not isinstance(queryset, self._queryset_class):
            queryset = queryset._chain(
                klass=self._queryset_class,
                model=self.model,
                using=self.db,
                hints=self._hints,
            )
        return queryset

    def check_history_disable_flag(self, instance, history_flag):
        # Get the model class from the instance
        model_class = type(instance)
        diasble_history = getattr(model_class, "history_disable", False)

        if diasble_history:
            history_flag = False

        return history_flag


    def bulk_create_with_rounding(self, instances, batch_size=1000, decimal_places=None):
        """
        Bulk create the instances with rounding the fields to the decimal places.

        Args:
            instances: List of instances to be created.
            batch_size: Batch size for bulk create operation.
            decimal_places: Decimal places to round the fields.

        Returns:
            List of instances created.
        """

        if not instances:
            return []

        first_instance = instances[0]

        # is tenant based model flag for checking the model is tenant based or not
        is_tenant_based_model = isinstance(first_instance, TenantBaseModel)

        if decimal_places is None and is_tenant_based_model:
            decimal_places = get_decimal_places(first_instance.account_id)

        # user flag for checking the user is present or not
        request_user = CurrentUserMiddleware.get_current_user()
        user_flag = request_user and not request_user.is_anonymous

        # fields_to_round_flag flag for rounding the fields
        fields_to_round = getattr(first_instance, 'round_fields', [])
        fields_to_round_flag = bool(fields_to_round) and is_tenant_based_model

        # created by flag for updated_by field updation
        created_by_flag = is_tenant_based_model and user_flag

        #history flag for checking the history model
        history_flag = hasattr(first_instance, 'history')

        history_flag = self.check_history_disable_flag(first_instance, history_flag)

        # process each instance
        for instance in instances:
            if created_by_flag:
                instance.created_by = request_user
                instance.updated_by = request_user
            if fields_to_round_flag:
                instance = pre_save_round_fields(instance, instance.account_id, fields_to_round, decimal_places=decimal_places)
            if history_flag:
                instance._change_reason = 'Bulk Create'

        # perform bulk create operation with history
        if history_flag:
            return bulk_create_with_history(instances, self.model, batch_size=batch_size)
        else:
            return super().bulk_create(instances, batch_size=batch_size)

    def bulk_update_with_rounding(self, instances, fields, batch_size=500, decimal_places=None):
        """
        Bulk update the instances with rounding the fields to the decimal places.

        Args:
            instances: List of instances to be updated.
            fields: List of fields to be updated.
            batch_size: Batch size for bulk update operation.
            decimal_places: Decimal places to round the fields.

        Returns:
            List of instances updated.
        """

        if not instances:
            return []

        first_instance = instances[0]

        current_timestamp = timezone.now()

        # is tenant based model flag for checking the model is tenant based or not
        is_tenant_based_model = isinstance(first_instance, TenantBaseModel)

        if decimal_places is None and is_tenant_based_model:
            decimal_places = get_decimal_places(first_instance.account_id)

        # user flag for checking the user is present or not
        request_user = CurrentUserMiddleware.get_current_user()
        user_flag = request_user and not request_user.is_anonymous

        # fields_to_round_flag flag for rounding the fields
        fields_to_round = getattr(first_instance, 'round_fields', [])
        fields_to_round = list(set(fields_to_round) & set(fields))
        fields_to_round_flag = bool(fields_to_round) and is_tenant_based_model

        # instance updation date flag for updation date field updation
        instance_updation_date_flag = hasattr(first_instance, 'updation_date') and 'updation_date' not in fields
        if instance_updation_date_flag:
            fields.append('updation_date')

        # updated by flag for updated_by field updation
        updated_by_flag = is_tenant_based_model and user_flag
        if updated_by_flag:
            fields.append('updated_by')

        #history flag for checking the history model
        history_flag = hasattr(first_instance, 'history')

        history_flag = self.check_history_disable_flag(first_instance, history_flag)

        # process each instance
        for instance in instances:
            # Update the last_modified date for each instance if updation date field is present in the model
            if instance_updation_date_flag:
                instance.updation_date = current_timestamp
            if updated_by_flag:
                instance.updated_by = request_user
            if fields_to_round_flag:
                instance = pre_save_round_fields(instance, instance.account_id, fields_to_round, decimal_places=decimal_places)
            if history_flag:
                instance._change_reason = 'Bulk Update'

        # perform bulk update operation with history
        if history_flag:
            updated_values = bulk_update_with_history(instances, self.model, fields, batch_size=batch_size)
        else:
            updated_values = super().bulk_update(instances, fields, batch_size=batch_size)

        self.invalidate_cache()
        return updated_values

    def invalidate_cache(self):
        if USE_CACHE_OPS:
            value = f"{self.model._meta.app_label}.{self.model._meta.model_name}"
            if value in CACHEOPS:
                invalidate_model(self.model)

    def perform_bulk_deletion(self, queryset, chunk_size=1000):
        """
        Deletes model objects in chunks to avoid memory issues and DB locks.

        :param queryset: Django QuerySet of the model objects to delete
        :param chunk_size: Number of objects to delete per chunk
        :return: Total number of objects deleted
        """
        total_deleted = 0

        # Early return if queryset is empty
        if not queryset.exists():
            return total_deleted

        while True:
            with transaction.atomic():
                # Get IDs for the current chunk
                ids = list(queryset.values_list('id', flat=True)[:chunk_size])

                # Break if no more objects to delete
                if not ids:
                    break

                # Delete the chunk and get the actual count
                deleted_count, _ = queryset.model.objects.filter(id__in=ids).delete()
                total_deleted += deleted_count

                # If we deleted fewer objects than chunk_size, we're done
                if len(ids) < chunk_size:
                    break

        return total_deleted


class BaseModelQuerySet(models.QuerySet):
    def update(self, **kwargs):
        updated = super().update(**kwargs)
        self.model.objects.invalidate_cache()  # Cache invalidation logic
        return updated

    def bulk_update(self, *args, **kwargs):
        updated = super().bulk_update(*args, **kwargs)
        self.model.objects.invalidate_cache()  # Cache invalidation logic
        return updated


class BaseModelManager(models.Manager):
    def get_queryset(self):
        return BaseModelQuerySet(self.model, using=self._db)

    def invalidate_cache(self):
        if USE_CACHE_OPS:
            value = f"{self.model._meta.app_label}.{self.model._meta.model_name}"
            if value in CACHEOPS:
                invalidate_model(self.model)


class BaseModel(models.Model):
    id = AutoField(primary_key=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    objects = BaseModelManager()

    class Meta:
        abstract = True

    @staticmethod
    def apply_cacheops(qs):
        """ Apply cacheops to the queryset if USE_CACHE_OPS is True."""
        if USE_CACHE_OPS:
            return qs.cache()
        return qs

@receiver(pre_save)
def pre_save_round(sender, instance, **kwargs):
    if hasattr(instance, 'round_fields'):
        fields_to_round = instance.round_fields

        # Define the fields to round and their decimal places in the concrete model class
        instance = pre_save_round_fields(instance, instance.account_id, fields_to_round)

class Role(BaseModel):

    ROLE_REMARKS_CHOICES = (
        (0, 'Stockone Managed'),
        (1, 'Customer Managed'),
    )
    history = HistoricalRecords()
    name = models.CharField(_("name"), max_length=150)
    code_name = models.CharField(_("code_name"), max_length=150)
    company = models.ForeignKey('CompanyMaster', on_delete=models.CASCADE, related_name='roles_company', null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='roles_cerated', null=True, blank=True)
    remarks = models.IntegerField(choices=ROLE_REMARKS_CHOICES, default=0)
    json_data= models.JSONField(default=dict, blank=True, null=True)
    permissions = models.ManyToManyField(
        Permission,
        verbose_name=_("permissions"),
        blank=True,
    )

    class Meta:
        verbose_name = 'Role'
        verbose_name_plural = 'Roles'
        unique_together = ['name', 'code_name', 'company']

    def __str__(self):
        return ' | '.join([str(self.company), str(self.created_by), str(self.name)])


class TenantBaseModel(BaseModel, TenantModelMixin):
    account = ForeignKey('wms_base.UserProfile', on_delete=models.CASCADE, related_name='%(class)ss_account', db_constraint= False, blank=True)
    tenant_id = 'account_id'
    objects = TenantManager()

    created_by = ForeignKey(
        User,
        related_name="%(class)s_created_by",
        null=True,
        blank=True,
        on_delete=models.SET_NULL
    )

    updated_by = ForeignKey(
        User,
        related_name="%(class)s_updated_by",
        null=True,
        blank=True,
        on_delete=models.SET_NULL
    )

    class Meta:
        abstract = True
        unique_together = ('id', 'account_id')

    class TenantMeta:
        tenant_field_name = 'account_id'

    def save(self, *args, **kwargs):
        request_user = CurrentUserMiddleware.get_current_user()
        if request_user and not request_user.is_anonymous:
            if not self.id:  # New object
                self.created_by = request_user
            self.updated_by = request_user

        super().save(*args, **kwargs)

    def set_user_id(self):
        pass

    def get_user_id(self):
        user_id = None
        if isinstance(self.user, (int, float)):
            user_id = self.user
        if isinstance(self.user, (User)):
            user_id = self.user.user_profile
        return user_id

    def get_account_id(self):
        self.set_user_id()
        user_id = self.get_user_id()
        return user_id

class WarehouseManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(warehouse_level=3)

    def get_company_warehouses(self, company_id, warehouse_level=3):
        """
        Filters and retrieves all UserProfile objects for a specific company and warehouse level.
        Args:
            company_id (int): The ID of the company to filter by.
            warehouse_level (int, optional): The warehouse level to filter by. Defaults to 3.

        Returns:
            models.QuerySet: A QuerySet of UserProfile objects filtered by company_id and warehouse_level.
        """
        return self.filter(company_id=company_id, warehouse_level=warehouse_level)

class UserProfile(TenantModelMixin, BaseModel):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    phone_number = models.CharField(max_length=32, default='', blank=True)
    birth_date = models.DateTimeField(auto_now=True)
    is_active = models.IntegerField(default=0)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    timezone = models.CharField(max_length=64, default='', blank=True)
    base_currency = models.CharField(max_length=5, choices=CURRENCY_CHOICES, default='INR')
    swx_id = models.IntegerField(default=None, blank=True, null=True)
    prefix = models.CharField(max_length=64, default='')
    location = models.CharField(max_length=60, default='', blank=True)
    city = models.CharField(max_length=60, default='', blank=True)
    state = models.CharField(max_length=60, default='', blank=True)
    country = models.CharField(max_length=60, default='', blank=True)
    pin_code = models.PositiveIntegerField(default=0)
    address = models.CharField(max_length=256, default='', blank=True)
    wh_address = models.CharField(max_length=256, default='', blank=True)
    wh_phone_number = models.CharField(max_length=32, default='', blank=True)
    gst_number = models.CharField(max_length=32, default='', blank=True)
    multi_warehouse = models.IntegerField(default=0, blank=True, db_index=True)
    multi_level_system = models.IntegerField(default=0, blank=True, db_index=True) # Added for GoMech Multi Level System.
    is_trail = models.IntegerField(default=0, blank=True)
    api_hash = models.CharField(max_length=256, default='', blank=True)
    setup_status = models.CharField(max_length=60, default='completed', blank=True)
    user_type = models.CharField(max_length=60, default='warehouse_user')
    warehouse_type = models.CharField(max_length=60, default='', blank=True)
    warehouse_level = models.IntegerField(default=0, blank=True)
    min_order_val = models.PositiveIntegerField(default=0)
    level_name = models.CharField(max_length=64, default='', blank=True)
    zone = models.CharField(max_length=64, default='', blank=True)
    cin_number = models.CharField(max_length=64, default='', blank=True)
    customer_logo = models.ImageField(upload_to='s3static/images/customer_logos/', storage=PublicMediaStorage(), blank=True, default='')
    bank_details = models.TextField(default='', blank=True)
    industry_type = models.CharField(max_length=32, default='', blank=True)
    order_prefix = models.CharField(max_length=32, default='', null=True, blank=True)
    pan_number = models.CharField(max_length=64, default='', blank=True)
    company = ForeignKey('CompanyMaster',on_delete=models.CASCADE, blank=True, null=True)
    reference_id = models.CharField(max_length=64, default='', null=True, blank=True)
    stockone_code = models.CharField(max_length=64, default='', null=True, blank=True)
    sap_code = models.CharField(max_length=64, default='', null=True, blank=True)
    place_of_supply = models.CharField(max_length=64, default='', null=True, blank=True)
    location_code = models.CharField(max_length=64, default='', null=True, blank=True)
    attune_id = models.IntegerField(default=None, blank=True, null=True)
    site_code = models.CharField(max_length=16, default='', blank=True)
    json_data= models.JSONField(null=True, blank=True)
    integrations_user =  models.BooleanField(default=False)
    tenant_id = 'id'
    objects = TenantManager()
    warehouse_objects = WarehouseManager()

    class Meta:
        db_table = 'USER_PROFILE'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['warehouse_level']),
            models.Index(fields=['warehouse_type']),
        ]

    def __str__(self):
        return str(self.user)

class CompanyMaster(BaseModel):

    history = HistoricalRecords()
    company_name = models.CharField(max_length=256, default='')
    company_code = models.CharField(max_length=3, default='com')
    address = models.CharField(max_length=256, default='', blank=True)
    city = models.CharField(max_length=64, default='', blank=True)
    state = models.CharField(max_length=64, default='', blank=True)
    country = models.CharField(max_length=64, default='', blank=True)
    pincode = models.CharField(max_length=64, default='', blank=True)
    phone_number = models.CharField(max_length=32, blank=True)
    email_id = models.EmailField(max_length=64, default='', blank=True)
    gstin_number = models.CharField(max_length=64, default='', blank=True)
    cin_number = models.CharField(max_length=64, default='', blank=True)
    pan_number = models.CharField(max_length=64, default='', blank=True)
    logo = models.ImageField(upload_to='s3static/images/companies/', storage=PrivateMediaStorage(), blank=True, default='')
    parent = ForeignKey("self",on_delete=models.CASCADE, blank=True, null=True)
    reference_id = models.CharField(max_length=64, default='', null=True, blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    custom_roles = models.BooleanField(default=False)
    default_roles = models.BooleanField(default=False)
    json_data = models.JSONField(null=True, blank=True, default=dict)
    class Meta:
        db_table = 'COMPANY_MASTER'

    def __str__(self):
        return str(self.company_name)

class CompanyRoles(BaseModel):
    company = ForeignKey(CompanyMaster,on_delete=models.CASCADE)
    role_name = models.CharField(max_length=64, default='')
    group = ForeignKey(Group,on_delete=models.CASCADE, default=None, blank=True, null=True)

    class Meta:
        db_table = 'COMPANY_ROLES'

    def __str__(self):
        return self.role_name

class UserRoleMapping(BaseModel):

    user = ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True)
    role_id = models.CharField(max_length=64, default='')
    role_type = models.CharField(max_length=32, choices=ROLE_TYPE_CHOICES, default='supplier')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'USER_ROLE_MAPPING'
        unique_together = ('user', 'role_id', 'role_type')
        indexes = [
            models.Index(fields=['user', 'role_id', 'role_type']),
        ]

class UserAddresses(BaseModel):

    user = ForeignKey(User,on_delete=models.CASCADE)
    shipping_address_id = models.PositiveIntegerField(blank=True, null=True)
    address_name = models.CharField(max_length=64)
    address_type = models.CharField(max_length=64)
    user_name = models.CharField(max_length=64)
    mobile_number = models.CharField(max_length=32)
    pincode = models.CharField(max_length=10)
    address = models.CharField(max_length=256, default='', blank=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'USER_ADDRESSES'

class UserAccessTokens(BaseModel):

    code = models.CharField(max_length=64, default='')
    user_profile = ForeignKey(UserProfile, on_delete=models.CASCADE, blank=True, null=True)
    access_token = models.CharField(max_length=1024, default='', blank=True, null=True)
    refresh_token = models.CharField(max_length=256, default='', blank=True, null=True)
    token_type = models.CharField(max_length=64)
    expires_in = models.IntegerField()
    app_host = models.CharField(max_length=64, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'USER_ACCESS_TOKENS'

    def __str__(self):
        return str(self.user_profile)

class UserGroups(BaseModel):

    user = ForeignKey(User,on_delete=models.CASCADE, db_index=True)
    admin_user = ForeignKey(User,on_delete=models.CASCADE, related_name='admin_user', blank=True, null=True, db_index=True)
    company = ForeignKey(CompanyMaster,on_delete=models.CASCADE, blank=True, null=True)

    class Meta:
        db_table = 'USER_GROUPS'
        indexes = [
            models.Index(fields=['user', 'admin_user']),
            models.Index(fields=['user', 'company']),
        ]

    def __str__(self):
        return str(self.user) + ' | ' + str(self.admin_user)

class UserPassword(models.Model):
    user = models.ForeignKey(User, related_name='password_records', on_delete=models.CASCADE, editable=False)
    password = models.CharField('Password hash', max_length=128, editable=False)
    date = models.DateTimeField('Date', auto_now_add=True, editable=False)

    class Meta:
        get_latest_by = 'date'
        ordering = ['-date']


@receiver(post_save, sender=User)
def create_password_record(sender, instance, created, **kwargs):
    try:
        latest_record = instance.password_records.latest()
        if latest_record.password == instance.password:
            return
    except ObjectDoesNotExist:
        pass
    instance.password_records.create(password=instance.password)

class AdminGroups(BaseModel):

    user = models.OneToOneField(User, on_delete=models.CASCADE, blank=True, null=True)
    group = models.OneToOneField(Group, on_delete=models.CASCADE)

    class Meta:
        db_table = 'ADMIN_GROUPS'

    def __str__(self):
        view_name = str(self.user) + ' | ' + str(self.group)
        return view_name

class StaffMaster(BaseModel):

    staff_name = models.CharField(max_length=64, default='')
    staff_code = models.CharField(max_length=64, default='')
    company = ForeignKey(CompanyMaster,on_delete=models.CASCADE, blank=True, null=True)
    user = ForeignKey(User,on_delete=models.CASCADE, blank=True, null=True, related_name='staff_warehouse')
    warehouse_type = models.CharField(max_length=64, default='')
    position = models.CharField(max_length=64,null=True, blank=True)
    email_id = models.EmailField(max_length=64, default='')
    reportingto_email_id = models.EmailField(max_length=64, null=True, blank=True)
    phone_number = models.CharField(max_length=32, null=True, blank=True)
    status = models.IntegerField(default=1)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)
    last_logout = models.DateTimeField(blank=True, null=True)
    json_data = models.JSONField(null=True, blank=True)
    class Meta:
        db_table = 'STAFF_MASTER'
        indexes = [
            models.Index(fields=['company', 'email_id']),
            models.Index(fields=['email_id']),
            models.Index(fields=['company', 'email_id', 'user']),
        ]

    def save(self, *args, **kwargs):
        new_json_data = kwargs.pop('json_data','')
        if not self.json_data:
            self.json_data = {}
        if new_json_data:
            self.json_data.update(new_json_data)
        super(StaffMaster, self).save(*args, **kwargs)

    def __str__(self):
        return str(self.user)

class POChoices(models.IntegerChoices):
    DEFAULT = 0, _('Default')
    MAKE = 1, _('Make')
    BUY = 2, _('Buy')

class StaffWarehouseMapping(BaseModel):

    staff = ForeignKey(StaffMaster, on_delete=models.CASCADE, blank=True, null=True)
    warehouse = ForeignKey(User, on_delete=models.CASCADE, blank=True, null=True)
    status = models.IntegerField(default=1)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'STAFF_WAREHOUSE_MAPPING'
        unique_together = ('staff', 'warehouse')
        indexes = [
            models.Index(fields=['staff', 'status', 'warehouse']),
        ]

    def __str__(self):
        return str(self.staff) + ' | ' + str(self.warehouse)

# #End Of Base Models Dont Touch This


class Integrations(TenantBaseModel):
    user = models.PositiveIntegerField()
    name = models.CharField(max_length=64, default='')
    api_instance = models.CharField(max_length=256, default='')
    client_id = models.CharField(max_length=64, default='')
    secret = models.CharField(max_length=256, default='')
    token_id = models.CharField(max_length=256, default='')
    token_secret = models.CharField(max_length=256, default='')
    extra_json = models.JSONField(default=dict, null=True, blank=True)
    retry_attempts = models.IntegerField(default=1)
    status = models.IntegerField(default=1)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'INTEGRATIONS'




class SSOTypes(TenantBaseModel):

    sso_name = models.CharField(max_length=32, unique=True)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SSO_TYPE'

    def __str__(self):
        return self.sso_name

class SSOIntegration(TenantBaseModel):

    company = ForeignKey(User,on_delete=models.CASCADE)
    domain = models.CharField(max_length=32, blank=True, null=True)
    relay_hash = models.CharField(max_length=64, blank=True, null=True)
    sso_type = ForeignKey(SSOTypes,on_delete=models.CASCADE)
    metadata_url = models.CharField(max_length=512, blank=True, null=True)
    sso_url = models.CharField(max_length=512, blank=True, null=True)
    certificate = models.TextField(blank=True, null=True)
    enforce_login = models.BooleanField(default=False)
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'SSO_INTEGRATION'
        unique_together = (('company', 'sso_type'))

    def __str__(self):
        return self.domain



class WeightingMachine(BaseModel):
    STATUS = (
        (0, 'InActive'),
        (1, 'Active'),
    )
    name = models.CharField(max_length=100)
    warehouse = ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    used_at = models.CharField(max_length=100, null=True, blank=True)
    regex = models.CharField(max_length=100)  # Regular expression for data abstraction
    status = models.IntegerField(choices=STATUS, default=1)
    extra_json = models.JSONField(default=dict, null=True, blank=True)

    class Meta:
        db_table = 'WEIGHTING_MACHINE'
        unique_together = (('name', 'warehouse'))

    def __str__(self):
        return self.name

class APIRatelimit(BaseModel):
    REQUEST_METHODS = (
        ('ALL', 'ALL'),
        ('GET', 'GET'),
        ('POST', 'POST'),
        ('PUT', 'PUT'),
        ('PATCH', 'PATCH'),
        ('DELETE', 'DELETE')
    )
    company = ForeignKey(CompanyMaster, on_delete=models.CASCADE)
    request_name = models.CharField(max_length=100)
    request_method = models.CharField(max_length=16, choices=REQUEST_METHODS)
    rate_number = models.IntegerField()
    rate_seconds = models.IntegerField()
    quota_number =  models.IntegerField(null=True, blank=True)
    quota_days = models.IntegerField(null=True, blank=True)

    class Meta:
        db_table = 'API_RATELIMIT'
        unique_together = (('company', 'request_name', 'request_method'),)

    def __str__(self):
        return ':'.join([str(self.company), self.request_name, self.request_method])

@receiver(post_save, sender=APIRatelimit)
def apilimit_post_save_handler(sender, instance, **kwargs):

    redis_wrapper = RedisJSONWrapper()

    company_name = instance.company.company_name
    api_name = instance.request_name
    api_method = instance.request_method
    rate_number = instance.rate_number
    rate_seconds = instance.rate_seconds
    quota_number = instance.quota_number
    quota_days = instance.quota_days

    #rateconfig key
    rate_config_key = f"{company_name}:{api_name}:{api_method}:rateconfig"

    #updating the rateconfig update
    level_configs  = {"rate_number": rate_number, "rate_seconds": rate_seconds, "quota_number": quota_number, "quota_days": quota_days}

    redis_wrapper.set(rate_config_key, level_configs)

    #delete the current user rate limit keys
    rate_limit_keys_pattren = f"*:{api_name}:{api_method}:ratelimit"
    redis_wrapper.delete_keys_with_suffix(rate_limit_keys_pattren)

    quota_limit_keys_pattren = f"*:{api_name}:{api_method}:quotalimit"
    redis_wrapper.delete_keys_with_suffix(quota_limit_keys_pattren)

@receiver(post_delete, sender=APIRatelimit)
def apilimit_post_delete_handler(sender, instance, **kwargs):

    redis_wrapper = RedisJSONWrapper()

    company_name = instance.company.company_name
    api_name = instance.request_name
    api_method = instance.request_method

    #rateconfig key
    rate_config_key = f"{company_name}:{api_name}:{api_method}:rateconfig"
    redis_wrapper.delete(rate_config_key)

    rate_limit_keys_pattren = f"*:{api_name}:{api_method}:ratelimit"
    redis_wrapper.delete_keys_with_suffix(rate_limit_keys_pattren)

    quota_limit_keys_pattren = f"*:{api_name}:{api_method}:quotalimit"
    redis_wrapper.delete_keys_with_suffix(quota_limit_keys_pattren)

class EmailConfig(BaseModel):

    company = ForeignKey(CompanyMaster, on_delete=models.CASCADE)
    from_email = models.CharField(max_length=128, help_text="email username example: <EMAIL>")
    host =  models.CharField(max_length=100, help_text="email host example: smtp.gmail.com")
    port = models.PositiveIntegerField(help_text="email port example: 587")
    username = models.CharField(max_length=128, help_text="email username example: <EMAIL>")
    password = models.CharField(max_length=128, help_text="email password example: abcxyz")
    use_tls = models.BooleanField(default=True)
    use_ssl = models.BooleanField(default=False)
    active = models.BooleanField(default=True)

    class Meta:
        db_table = 'EMAIL_CONFIG'

    def clean(self):
        if self.use_tls and self.use_ssl:
            raise ValidationError('Both TLS and SSL are mutually Exclusive')

        if self.active and EmailConfig.objects.filter(company=self.company, active=True).exclude(id=self.id).exists():
            raise ValidationError('Only one company can have one active record.')


    def __str__(self):
        return self.from_email + str(self.company)


class WarehouseConfig(BaseModel):

    warehouse = ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    roles = models.ManyToManyField(Role, related_name='warehouse_roles', blank=True, help_text="Roles to be excluded for security")

    check_ip = models.BooleanField(default=False)
    allowed_ips = models.TextField(null=True, blank=True,  help_text="comma seperated list of ips", validators=[validate_ip_list])
    restrict_ips = models.TextField(null=True, blank=True, help_text="comma seperated list of ips", validators=[validate_ip_list])

    check_location = models.BooleanField(default=False)
    warehouse_lat = models.FloatField(null=True, blank=True, help_text="warehouse latitude")
    warehouse_long = models.FloatField(null=True, blank=True, help_text="warehouse longitude")
    login_range = models.IntegerField(default=0, help_text="login range in meters based on warehouse lat long")


    class Meta:
        db_table = 'WAREHOUSE_CONFIG'
        unique_together = ('warehouse',)

    def __str__(self):
        return self.warehouse.username

    def has_no_roles(self):
        """
        Check if the roles field is empty.
        """
        return not self.roles.exists()


@receiver(post_save, sender=WarehouseConfig)
def securityconf_post_save_handler(sender, instance, **kwargs):

    redis_wrapper = RedisJSONWrapper()

    warehouse = instance.warehouse.username
    role_ids = [role.id for role in instance.roles.all()]

    check_ip = instance.check_ip
    allowed_ips = instance.allowed_ips
    restrict_ips = instance.restrict_ips

    check_location = instance.check_location
    warehouse_lat = instance.warehouse_lat
    warehouse_long = instance.warehouse_long
    login_range = instance.login_range

    #security-config key
    security_config_key = f"securityconf:{warehouse}"

    #updating the security-config
    security_configs  = {"roles": role_ids, "check_ip": check_ip, "allowed_ips": allowed_ips, "restrict_ips": restrict_ips, "check_location": check_location, "warehouse_lat": warehouse_lat, "warehouse_long": warehouse_long, "login_range": login_range}

    redis_wrapper.set(security_config_key, security_configs)

@receiver(post_delete, sender=WarehouseConfig)
def securityconf_post_delete_handler(sender, instance, **kwargs):

    redis_wrapper = RedisJSONWrapper()
    warehouse = instance.warehouse.username

    #security-config key
    security_config_key = f"securityconf:{warehouse}"

    redis_wrapper.delete(security_config_key)

@receiver(m2m_changed, sender=WarehouseConfig.roles.through)
def securityconf_roles_changed(sender, instance, action, **kwargs):
    if action in ['post_add', 'post_remove', 'post_clear']:
        redis_wrapper = RedisJSONWrapper()
        warehouse = instance.warehouse.username

        role_ids = [role.id for role in instance.roles.all()]
        security_config_key = f"securityconf:{warehouse}"
        security_configs = redis_wrapper.get(security_config_key)
        security_configs['roles'] = role_ids

        redis_wrapper.set(security_config_key, security_configs)

class CompanyLevelConfigs(BaseModel):
        CONFIG_TYPE_CHOICES = [
            ('text', 'Text'),
            ('json', 'JSON'),
            ('boolean', 'Boolean'),
            ('number', 'Number'),
        ]

        company = ForeignKey(CompanyMaster, on_delete=models.CASCADE)
        config_display_name = models.CharField(max_length=64)
        config_key = models.CharField(max_length=64)
        config_value = models.JSONField(default=dict)
        config_category = models.CharField(max_length=64, default='General')
        config_type = models.CharField(max_length=256, choices=CONFIG_TYPE_CHOICES, default='text')

        class Meta:
            db_table = 'COMPANY_LEVEL_CONFIGS'
            unique_together = ('company', 'config_key')

        def __str__(self):
            return self.config_key

        def validate_config_value(self, config_type, config_value):

            if config_type == 'number':
                if not isinstance(config_value, (int, float)):
                    raise ValidationError({'config_value': 'Invalid number'})

            elif config_type == 'boolean':
                if not isinstance(config_value, bool):
                    raise ValidationError({'config_value': 'Invalid boolean'})

            elif config_type == 'text':
                if not isinstance(config_value, str):
                    raise ValidationError({'config_value': 'Invalid string'})

            elif config_type == 'json':
                try:
                    json.loads(json.dumps(config_value))  # Ensure the value can be serialized as JSON
                except (TypeError, ValueError):
                    raise ValidationError({'config_value': 'Invalid JSON data'})

        def clean(self):
            super().clean()
            self.validate_config_value(self.config_type, self.config_value)

        def save(self, *args, **kwargs):
            self.validate_config_value(self.config_type, self.config_value)
            super().save(*args, **kwargs)


class RoutingConfiguration(BaseModel):

    DB_CHOICES = (
        ("default", "default"),
        ("reports", "reports"),
        ("replica", "replica")
    )
    db_name = models.CharField(max_length=255, choices=DB_CHOICES)
    url_path = models.CharField(max_length=255, help_text="Select path for routing")
    datatable_name = models.CharField(max_length=255, help_text="Select datatable for routing")
    default_db_only = ArrayField(
        models.CharField(max_length=255),
        blank=True,
        default=list,
        help_text="List of models or tables that use only the default database"
    )
    active = models.BooleanField(default=False)

    class Meta:
        db_table = 'ROUTING_CONFIGURATION'
        unique_together = ('url_path', 'datatable_name')

    def __str__(self):
        return self.db_name

@receiver(post_save, sender=RoutingConfiguration)
def routerconfiguration_post_save_handler(sender, instance, **kwargs):

    redis_wrapper = RedisJSONWrapper(database=2)

    url_path = instance.url_path
    datatable_name = instance.datatable_name
    default_db_only = instance.default_db_only
    active = instance.active

    #routing-config key
    routing_config_key = f"dbrouting:{url_path}"
    if url_path == "core:Datatable View":
        routing_config_key = f"dbrouting:{url_path}:{datatable_name}"

    if active:
        configs = {
            "db_name": instance.db_name,
            "url_path": url_path,
            "datatable_name": datatable_name,
            "default_db_only": default_db_only,
            "active": active
        }
        redis_wrapper.set(routing_config_key, configs)
    else:
        redis_wrapper.delete(routing_config_key)

@receiver(post_delete, sender=RoutingConfiguration)
def routerconfiguration_post_delete_handler(sender, instance, **kwargs):

    redis_wrapper = RedisJSONWrapper(database=2)
    #routing-config key
    routing_config_key = f"dbrouting:{instance.url_path}"
    if instance.url_path == "core:Datatable View":
        routing_config_key = f"dbrouting:{instance.url_path}:{instance.datatable_name}"

    redis_wrapper.delete(routing_config_key)


class InfraConfig(models.Model):
    infra_name = models.CharField(max_length=64, default='')
    infra_value = models.CharField(max_length=256, default='')
    creation_date = models.DateTimeField(auto_now_add=True)
    updation_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'INFRA_CONFIG'
        unique_together = ('infra_name',)

@receiver(post_save, sender=InfraConfig)
def save_infra_config_to_redis(sender, instance, created, **kwargs):
    """
    Signal to save/update InfraConfig record in Redis when saved/updated in DB
    """
    redis_key = f"infra_config:{instance.infra_name}"
    redis_data = instance.infra_value
    cache.add(redis_key, redis_data, timeout=None)

@receiver(post_delete, sender=InfraConfig)
def delete_infra_config_from_redis(sender, instance, **kwargs):
    """
    Signal to delete InfraConfig record from Redis when deleted from DB
    """
    redis_key = f"infra_config:{instance.infra_name}"
    cache.delete(redis_key)

class ScriptModel(BaseModel):
    SCRIPT_TYPES = (
        ('pre', 'pre-Script'),
        ('post', 'Post-Script'),
    )

    METHOD_TYPES = (
        ('GET', 'GET'),
        ('POST', 'POST'),
        ('PUT', 'PUT'),
        ('PATCH', 'PATCH'),
    )

    TARGET_TYPES = (
        ('function', 'Function'),
        ('method', 'Class Method'),
    )

    name = models.CharField(max_length=256)
    script_type = models.CharField(max_length=4, choices=SCRIPT_TYPES)
    target_name = models.CharField(max_length=256, help_text="Format: 'app.module.function' or 'app.module.ClassName'")
    target_type = models.CharField(max_length=10, choices=TARGET_TYPES, help_text="Specify if target is a function or class method")
    api_name = models.CharField(max_length=256, help_text="API name for the script")
    http_method = models.CharField(max_length=10, choices=METHOD_TYPES)
    code = models.TextField(help_text="Must Contain a 'run(context)' function")
    skip_original_function = models.BooleanField(default=False, help_text="If True, skips the original function and uses script result only")
    script_users = models.ForeignKey("ScriptUsers", on_delete=models.CASCADE, help_text="Named user set this script applies to")
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='%(class)s_created_by')
    updated_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='%(class)s_updated_by')

    class Meta:
        db_table = 'SCRIPT_MODEL'
        unique_together = ('target_name', 'script_type', 'name', 'api_name', 'http_method')


    def __str__(self):
        return f"{self.name} - {self.name} for {self.target_name} at {self.api_name} ({self.http_method})"

    def clean(self):
        try:
            ScriptValidator.validate_code(self.code)
        except ValidationError as e:
            raise ValidationError({'code': e.message})  # Targets the 'code' field

    def save(self, *args, **kwargs):
        # Ensures validation is performed before saving
        self.full_clean()
        super().save(*args, **kwargs)


class ScriptUsers(BaseModel):
    name = models.CharField(max_length=100, unique=True, help_text="Unique name for this user set (e.g., 'Company A Team')")
    companies = models.ManyToManyField(CompanyMaster, blank=True, help_text="Companies this script applies to (leave empty for all)")
    warehouses = models.ManyToManyField(User, related_name='script_warehouses', blank=True, help_text="Warehouse users this script applies to (leave empty for all)")
    subusers = models.ManyToManyField(User, related_name='script_subusers', blank=True, help_text="Sub-users this script applies to (leave empty for all)" )

    def __str__(self):
        return self.name

    @property
    def cached_companies(self):
        qs = self.companies.values_list('company_name', flat=True)
        return self.apply_cacheops(qs)

    @property
    def cached_warehouses(self):
        qs = self.warehouses.values_list('username', flat=True)
        return self.apply_cacheops(qs)

    @property
    def cached_subusers(self):
        qs = self.subusers.values_list('username', flat=True)
        return self.apply_cacheops(qs)

    class Meta:
        db_table = 'SCRIPT_USERS'
